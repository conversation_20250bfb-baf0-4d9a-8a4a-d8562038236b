<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\StyleId;

use App\GoogleCsa\Helper\GoogleStyleIdValidator;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameter;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdProviderInterface;
use App\GoogleCsa\StyleId\Provider\ArticleStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\DebugStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\DisplaySearchRelatedStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\DisplaySearchRelatedWebStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\MicrosoftSearchRelatedWebStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\SearchStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\TrackingEntryStyleIdProvider;
use App\GoogleCsa\StyleId\Provider\WebSearchStyleIdProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReaderStub;

final class GoogleCsaStyleIdParameterTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testStyleIdProviderOrder(): void
    {
        // This list is hard coded, not sorted by reflection values.
        // This forces the developer to update this list if something is changed.
        // Ordered from highest to lowest priority.
        $expectedStyleIdProviderOrder = [
            DebugStyleIdProvider::class,
            TrackingEntryStyleIdProvider::class,
            DisplaySearchRelatedStyleIdProvider::class,
            ArticleStyleIdProvider::class,
            DisplaySearchRelatedWebStyleIdProvider::class,
            MicrosoftSearchRelatedWebStyleIdProvider::class,
            WebSearchStyleIdProvider::class,
            SearchStyleIdProvider::class,
        ];

        /** @var GoogleCsaStyleIdParameter $googleCsaStyleIdParameter */
        $googleCsaStyleIdParameter = self::getContainer()->get(GoogleCsaStyleIdParameter::class);

        $reflectionClass = new \ReflectionClass($googleCsaStyleIdParameter);
        $styleIdProviders = $reflectionClass
            ->getProperty('googleCsaStyleIdProviders')
            ->getValue($googleCsaStyleIdParameter);

        $actualStyleIdProviderOrder = array_map(
            static fn (GoogleCsaStyleIdProviderInterface $styleIdProvider) => $styleIdProvider::class,
            iterator_to_array($styleIdProviders),
        );

        self::assertSame($expectedStyleIdProviderOrder, $actualStyleIdProviderOrder);
    }

    public function testAllStyleIdProvidersHaveAUnitTest(): void
    {
        $unitTestCaseDir = __DIR__.'/../../../Unit/GoogleCsa/StyleId/Provider';

        /** @var GoogleCsaStyleIdParameter $googleCsaStyleIdParameter */
        $googleCsaStyleIdParameter = self::getContainer()->get(GoogleCsaStyleIdParameter::class);

        $reflectionClass = new \ReflectionClass($googleCsaStyleIdParameter);
        $styleIdProviders = $reflectionClass
            ->getProperty('googleCsaStyleIdProviders')
            ->getValue($googleCsaStyleIdParameter);

        foreach ($styleIdProviders as $styleIdProvider) {
            $styleIdProviderClass = new \ReflectionClass($styleIdProvider);
            $styleIdProviderClassName = $styleIdProviderClass->getShortName();
            $styleIdProviderTestClassName = $styleIdProviderClassName.'Test';
            $styleIdProviderTestFilePath = sprintf(
                '%s/%s.php',
                $unitTestCaseDir,
                $styleIdProviderTestClassName,
            );
            $testExists = realpath($styleIdProviderTestFilePath) !== false;

            self::assertTrue(
                $testExists,
                sprintf(
                    'Google CSA style ID provider "%s" does not have a required unit test',
                    $styleIdProvider::class,
                ),
            );
        }
    }

    public function testReturnsStyleId(): void
    {
        $this->stubs()->request()->getDebugRequest()->setForceStyleId(null);

        $this->stubs()->localeSettingsHelper()
            ->setLocale('de_DE');

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setSlug('example');

        /** @var DomainToBrandMapReaderStub $domainToBrandMapReaderStub */
        $domainToBrandMapReaderStub = self::getContainer()->get(DomainToBrandMapReaderStub::class);
        $domainToBrandMapReaderStub->setDomainToBrandMap(
            [
                'localhost' => 'example',
            ],
        );

        $this->setRequest(
            Request::create('/'),
        );

        /** @var GoogleCsaStyleIdParameter $googleCsaStyleIdParameter */
        $googleCsaStyleIdParameter = self::getContainer()->get(GoogleCsaStyleIdParameter::class);

        // No matter which provider is used in any situation,
        // the GoogleCsaStyleIdParameter should return a valid style ID.
        $actualStyleId = $googleCsaStyleIdParameter->getStyleId();
        self::assertTrue($actualStyleId > 0);
        self::assertTrue(GoogleStyleIdValidator::isValid($actualStyleId));
    }
}
