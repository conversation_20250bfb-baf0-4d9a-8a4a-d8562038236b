<?php

declare(strict_types=1);

namespace Tests\Integration\Debug\Url;

use App\Ads\AdProvider;
use App\Debug\Request\DebugRequestInterface;
use App\Debug\Url\DebugRequestUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Office\Request\OfficeRequestStub;

class DebugRequestUrlParametersProviderTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private DebugRequestStub $debugRequestStub;

    private OfficeRequestStub $officeRequestStub;

    private DebugRequestUrlParametersProvider $debugRequestUrlParametersProvider;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->debugRequestStub = $this->stubs()->request()->getDebugRequest();
        $this->officeRequestStub = $this->stubs()->request()->getOfficeRequest();

        /** @var DebugRequestUrlParametersProvider $debugRequestUrlParametersProvider */
        $debugRequestUrlParametersProvider = self::getContainer()->get(DebugRequestUrlParametersProvider::class);
        $this->debugRequestUrlParametersProvider = $debugRequestUrlParametersProvider;
    }

    /**
     * @return mixed[]
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        return [
            'not an office request'                         => [
                'debugRequestStub'      => static function (DebugRequestStub $debugRequestStub): void {
                    $debugRequestStub
                        ->setDebugInfo()
                        ->setDisableBingAds()
                        ->setCountryCode('US');
                },
                'officeRequestStub'     => static function (OfficeRequestStub $officeRequestStub): void {
                    $officeRequestStub->setIsOffice(false);
                },
                'expectedUrlParameters' => [],
            ],
            'office request, but no debug options active'   => [
                'debugRequestStub'      => static function (DebugRequestStub $debugRequestStub): void {
                    $debugRequestStub
                        ->setDebugInfo(false)
                        ->setDisableBingAds(false)
                        ->setDisableContentPage(false)
                        ->setDisableContentPages(false)
                        ->setDisableGoogleAds(false)
                        ->setDisableProfiler(false)
                        ->setEnableCheq(false)
                        ->setEnableModule(false)
                        ->setForceBotSearch(false)
                        ->setForceCacheRefresh(false)
                        ->setForceStyleId(null)
                        ->setForceMockSearch(false)
                        ->setForcePrimaryAdsType(null)
                        ->setCountryCode(null)
                        ->setPreferCsapiServer(null)
                        ->setSplitTestVariant(null)
                        ->setDebugForceCsaContainerPrefix(null)
                        ->setDebugForceCsaContainerSuffix(null)
                        ->setStatusCode(null)
                        ->setUserIp(null)
                        ->setIsAdBot(false)
                        ->setIsFriendlyBot(false)
                        ->setIsCustomError(false)
                        ->setRateLimitExceeded(false)
                        ->setShowFixedStats(false)
                        ->setShowGoogleTestAd(false)
                        ->setShowUnpublishedContentPages(false)
                        ->setContentPageHomeType(null)
                        ->setTemplateOverride(null);
                },
                'officeRequestStub'     => static function (OfficeRequestStub $officeRequestStub): void {
                    $officeRequestStub->setIsOffice(true);
                },
                'expectedUrlParameters' => [],
            ],
            'office request, with all debug options active' => [
                'debugRequestStub'      => static function (DebugRequestStub $debugRequestStub): void {
                    $debugRequestStub
                        ->setDebugInfo()
                        ->setDisableBingAds()
                        ->setDisableContentPage()
                        ->setDisableContentPages()
                        ->setDisableGoogleAds()
                        ->setDisableProfiler()
                        ->setEnableCheq()
                        ->setEnableModule()
                        ->setForceBotSearch()
                        ->setForceCacheRefresh()
                        ->setForceStyleId(1234)
                        ->setForceMockSearch()
                        ->setForcePrimaryAdsType(AdProvider::TYPE_BING)
                        ->setCountryCode('NL')
                        ->setPreferCsapiServer('csapi123')
                        ->setSplitTestVariant('abc')
                        ->setDebugForceCsaContainerPrefix('prefix')
                        ->setDebugForceCsaContainerSuffix('suffix')
                        ->setStatusCode(302)
                        ->setUserIp('*********')
                        ->setIsAdBot()
                        ->setIsFriendlyBot()
                        ->setIsCustomError()
                        ->setRateLimitExceeded()
                        ->setShowFixedStats()
                        ->setShowGoogleTestAd()
                        ->setShowUnpublishedContentPages()
                        ->setContentPageHomeType('home_7')
                        ->setTemplateOverride('template_123');
                },
                'officeRequestStub'     => static function (OfficeRequestStub $officeRequestStub): void {
                    $officeRequestStub->setIsOffice(true);
                },
                'expectedUrlParameters' => [
                    DebugRequestInterface::PARAMETER_DEBUG_COUNTRY_CODE                   => 'NL',
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_BING_ADS               => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_CONTENT_PAGE           => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_CONTENT_PAGES          => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_GOOGLE_ADS             => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_PROFILER               => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_ENABLE_CHEQ                    => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_ENABLE_MODULE                  => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_BOT_SEARCH               => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_CACHE_REFRESH            => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_MOCK_SEARCH              => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_PRIMARY_ADS_TYPE         => AdProvider::TYPE_BING,
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_STYLE_ID                 => '1234',
                    DebugRequestInterface::PARAMETER_DEBUG_INFO                           => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_IS_AD_BOT                      => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_IS_FRIENDLY_BOT                => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_IS_CUSTOM_ERROR                => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_PREFER_CSAPI_SERVER            => 'csapi123',
                    DebugRequestInterface::PARAMETER_DEBUG_RATE_LIMIT_EXCEEDED            => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_FIXED_STATS               => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_GOOGLE_TEST_AD            => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_UNPUBLISHED_CONTENT_PAGES => '1',
                    DebugRequestInterface::PARAMETER_DEBUG_SPLIT_TEST_VARIANT             => 'abc',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_PREFIX     => 'prefix',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_SUFFIX     => 'suffix',
                    DebugRequestInterface::PARAMETER_DEBUG_STATUS_CODE                    => '302',
                    DebugRequestInterface::PARAMETER_DEBUG_USER_IP                        => '*********',
                    DebugRequestInterface::PARAMETER_DEBUG_CONTENT_PAGE_HOME_TYPE         => 'home_7',
                    DebugRequestInterface::PARAMETER_DEBUG_TEMPLATE_OVERRIDE              => 'template_123',
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(
        callable $debugRequestStub,
        callable $officeRequestStub,
        array $expectedUrlParameters
    ): void
    {
        $debugRequestStub($this->debugRequestStub);
        $officeRequestStub($this->officeRequestStub);

        $actualUrlParameters = $this->debugRequestUrlParametersProvider
            ->getPersistentUrlParameters(PersistentUrlParametersPageType::DEFAULT);

        // Order is irrelevant
        ksort($actualUrlParameters);
        ksort($expectedUrlParameters);

        self::assertSame($expectedUrlParameters, $actualUrlParameters);
    }
}
