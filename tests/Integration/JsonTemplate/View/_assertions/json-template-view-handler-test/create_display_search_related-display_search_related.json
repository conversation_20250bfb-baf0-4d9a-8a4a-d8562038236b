{"composite_search_api_requests": [{"content_page": {"enabled": true, "is_homepage": null, "public_id": null, "paragraph_amount": null, "is_adult": null}, "related_terms": {"enabled": true, "amount": 12}}], "container": {"id": "container-0", "type": "container", "layout": "dsr", "mode": null, "font": null, "components": [{"id": "brand_logo-1", "type": "brand_logo", "componentSpaceModifiers": [], "layout": "default", "logoDarkMode": false, "linkToHome": true}, {"id": "columns-26", "type": "columns", "layout": "default", "one": [{"id": "has_content_page-27", "type": "has_content_page", "matchingSegment": [{"id": "content_page_header-2", "type": "content_page_header", "componentSpaceModifiers": ["top"], "layout": "default", "showReadTime": true, "showPageNumber": true}, {"id": "content_page_title-3", "type": "content_page_title", "componentSpaceModifiers": [], "layout": "default"}, {"id": "content_page_excerpt-4", "type": "content_page_excerpt", "componentSpaceModifiers": ["top", "bottom"], "maxLength": 200, "startAfterLength": null, "splitOnLineEnd": true, "layout": "default"}], "nonMatchingSegment": [{"id": "title-5", "type": "title", "componentSpaceModifiers": ["top"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.title", "titleHighlightTranslationId": null, "subtitleTranslationId": "content_page.subtitle", "layout": "dsr"}, {"id": "organic_results_with_fallback-28", "type": "organic_results_with_fallback", "resultsComponent": {"id": "organic_content_page_results-6", "type": "organic_content_page_results", "componentSpaceModifiers": ["bottom", "top"], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "resultImageLink": true, "showResultDisplayUrl": true, "linkToActiveBrand": true, "maxDescriptionLength": 130, "layout": "dsr"}, "fallbackComponent": {"id": "organic_results-7", "type": "organic_results", "componentSpaceModifiers": ["bottom", "top"], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": 130, "layout": "dsr"}}]}, {"id": "google_related_terms-8", "type": "google_related_terms", "amount": 6, "route": "route_display_search_related_web", "target": "content", "termsUrlParameterEnabled": true, "containerSuffix": null, "fallbackRelatedTerms": {"componentSpaceModifiers": [], "layout": "fallback", "amount": 6, "zone": "i", "route": "route_display_search_related_web", "columns": 1, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}}, {"id": "has_content_page-29", "type": "has_content_page", "matchingSegment": [{"id": "content_page_excerpt-9", "type": "content_page_excerpt", "componentSpaceModifiers": [], "maxLength": null, "startAfterLength": 200, "splitOnLineEnd": true, "layout": "default"}, {"id": "content_page_paragraph-10", "type": "content_page_paragraph", "componentSpaceModifiers": ["top", "bottom"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}, {"id": "content_page_paragraph-11", "type": "content_page_paragraph", "componentSpaceModifiers": ["top", "bottom"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}], "nonMatchingSegment": [{"id": "organic_results_with_fallback-30", "type": "organic_results_with_fallback", "resultsComponent": {"id": "organic_content_page_results-12", "type": "organic_content_page_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "resultImageLink": true, "showResultDisplayUrl": true, "linkToActiveBrand": true, "maxDescriptionLength": null, "layout": "dsr"}, "fallbackComponent": {"id": "organic_results-13", "type": "organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": null, "layout": "dsr"}}]}, {"id": "google_related_terms-14", "type": "google_related_terms", "amount": 6, "route": "route_display_search_related_web", "target": "content", "termsUrlParameterEnabled": true, "containerSuffix": null, "fallbackRelatedTerms": {"componentSpaceModifiers": [], "layout": "fallback", "amount": 6, "zone": "i", "route": "route_display_search_related_web", "columns": 1, "showTitle": true, "keywordHighlight": null, "repeatTerms": false}}, {"id": "has_content_page-31", "type": "has_content_page", "matchingSegment": [{"id": "content_page_paragraph-15", "type": "content_page_paragraph", "componentSpaceModifiers": ["top-l", "bottom-l"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}, {"id": "content_page_image-16", "type": "content_page_image", "componentSpaceModifiers": [], "layout": "default", "align": "center", "showCaption": true, "fallbackImage": null}, {"id": "content_page_paragraphs-17", "type": "content_page_paragraphs", "componentSpaceModifiers": [], "layout": "container", "amount": null, "paragraphComponent": {"componentSpaceModifiers": ["top-l", "bottom-l"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}}, {"id": "content_page_footer-18", "type": "content_page_footer", "componentSpaceModifiers": [], "layout": "default"}, {"id": "share_page-19", "type": "share_page", "componentSpaceModifiers": [], "layout": "default", "share": "content_page"}, {"id": "scroll_to_top-20", "type": "scroll_to_top", "layout": "default"}, {"id": "split_test_matches-32", "type": "split_test_matches", "oneOfVariants": ["tsbpd"], "matchingSegment": [{"id": "content_page_results-33", "type": "content_page_results", "componentSpaceModifiers": ["top-xxl", "bottom-xxl"], "amountInRow": 3, "ignoreQuery": false, "resetCounter": true, "layout": "card-10", "titleComponent": {"componentSpaceModifiers": ["top-xl"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.top_stories.title", "titleHighlightTranslationId": "content_page.top_stories.title_highlight", "subtitleTranslationId": null, "layout": "content-3"}}], "nonMatchingSegment": []}], "nonMatchingSegment": [{"id": "organic_error_message-21", "type": "organic_error_message"}]}], "two": [], "three": [], "mainColumn": null, "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "footer-34", "type": "footer", "layout": "default", "components": [{"id": "columns-35", "type": "columns", "layout": "default", "one": [{"id": "search_bar-22", "type": "search_bar", "componentSpaceModifiers": ["top-l"], "layout": "default", "showSearchQuery": false, "allowStartQuerySearch": true, "autofocus": false}, {"id": "footer_logo-23", "type": "footer_logo", "layout": "default", "logoDarkMode": false, "logoStyleFilter": null, "hideOnDesktop": true}, {"id": "disclaimer-24", "type": "disclaimer", "componentSpaceModifiers": [], "layout": "default"}], "two": [], "three": [], "mainColumn": null, "section": "footer", "sectionVisible": true, "sectionCssProperties": ["background"]}, {"id": "footer_navigation-25", "type": "footer_navigation", "componentSpaceModifiers": [], "showAbout": true, "showContact": true, "showCopyright": true, "showDisclaimer": true, "showPrivacy": true, "layout": "dsr", "logoDarkMode": false}]}]}}