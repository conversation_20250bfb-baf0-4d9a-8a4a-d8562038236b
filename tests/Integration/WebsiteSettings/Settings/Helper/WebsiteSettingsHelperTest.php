<?php

declare(strict_types=1);

namespace Tests\Integration\WebsiteSettings\Settings\Helper;

use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Monolog\Level;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\Helper\BrandConfigTestHelper;
use Tests\Integration\WebsiteSettings\AbstractWebsiteSettingsTestCase;
use Tests\Stub\WebsiteSettings\Configuration\WebsiteConfigurationHelperStub;
use Visymo\Shared\Infrastructure\Stub\Domain\Logger\MemoryLoggerStub;

class WebsiteSettingsHelperTest extends AbstractWebsiteSettingsTestCase
{
    private WebsiteSettingsHelper $websiteSettingsHelper;

    private WebsiteConfigurationHelperStub $websiteConfigurationHelperStub;

    private MemoryLoggerStub $logger;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stubs()
            ->moduleSettings()
            ->getWebSearch()
            ->setEnabled()
            ->setStyleId(**********)
            ->create();

        /** @var WebsiteSettingsHelper $websiteSettingsHelper */
        $websiteSettingsHelper = self::getContainer()->get(WebsiteSettingsHelper::class);
        $this->websiteSettingsHelper = $websiteSettingsHelper;

        /** @var WebsiteConfigurationHelperStub $websiteConfigurationHelperStub */
        $websiteConfigurationHelperStub = self::getContainer()->get(WebsiteConfigurationHelperStub::class);
        $this->websiteConfigurationHelperStub = $websiteConfigurationHelperStub;

        /** @var MemoryLoggerStub $logger */
        $logger = self::getContainer()->get(MemoryLoggerStub::class);
        $this->logger = $logger;
    }

    /**
     * Test request with valid brand JSON config
     *
     * @return mixed[]
     *
     * {@see BrandConfigTestHelper::getFoodBrandConfig()}
     */
    public static function requestDataProvider(): array
    {
        return [
            'no_account'                         => [
                'url'                      => sprintf(
                    'https://%s/',
                    BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM,
                ),
                'expectedResponseFilePath' => __DIR__.'/_assertions/no_account.json',
            ],
            'google_account_with_google_adsense' => [
                'url'                      => sprintf(
                    'https://%s/ws?q=pizza&ac=%u',
                    BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM,
                    BrandConfigTestHelper::CONFIG_ACCOUNT_ID_3,
                ),
                'expectedResponseFilePath' => __DIR__.'/_assertions/google_account_with_google_adsense.json',
            ],
            'google_account_with_bing_ads'       => [
                'url'                      => sprintf(
                    'https://%s/ws?q=pizza&ac=%u&locale=%s',
                    BrandConfigTestHelper::CONFIG_DOMAIN_WWW_BRAND_CA,
                    BrandConfigTestHelper::CONFIG_ACCOUNT_ID_120,
                    'de_DE',
                ),
                'expectedResponseFilePath' => __DIR__.'/_assertions/google_account_with_bing_ads.json',
            ],
            'no_ad_provider_enabled'             => [
                'url'                      => sprintf(
                    'https://%s/ws?q=pizza&ac=%u&locale=%s',
                    BrandConfigTestHelper::CONFIG_DOMAIN_WWW_BRAND_CA,
                    BrandConfigTestHelper::CONFIG_ACCOUNT_ID_46,
                    'de_DE',
                ),
                'expectedResponseFilePath' => __DIR__.'/_assertions/no_ad_provider_enabled.json',
            ],
        ];
    }

    #[DataProvider('requestDataProvider')]
    public function testRequest(string $url, string $expectedResponseFilePath): void
    {
        $websiteConfiguration = $this->brandConfigTestHelper->getFoodWebsiteConfiguration();
        $this->websiteConfigurationHelperStub->setWebsiteConfiguration($websiteConfiguration);

        $request = Request::create($url);

        // User IP is not part of the test, but 'X-Forwarded-For' header is validated in the RequestInfoFactory
        // and will trigger a log if not set
        $request->headers->set('X-Forwarded-For', '127.0.0.1');

        $this->handleRequestRoute($request);
        $routeInfo = $this->getRouteInfoFromRequest($request);
        $request->attributes->add($routeInfo);
        $this->setRequest($request);

        $websiteSettings = $this->websiteSettingsHelper->getSettings();

        $actualOutputData = [
            'website_settings' => $websiteSettings->toArrayWithoutDisabledProperties(),
            'log'              => $this->logger->getLogsGreaterThanOrEqual(Level::Warning),
        ];

        $this->validateActualOutputData(
            $actualOutputData,
            $expectedResponseFilePath,
        );
    }
}
