<?php

declare(strict_types=1);

namespace Tests\Integration\ConversionTracking\Logging;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Logging\ConversionLogger;
use App\ConversionTracking\TrackingOrder\Type\OnePerClickIdTrackingOrder;
use App\ConversionTracking\TrackingOrder\Type\OnePerPageviewIdTrackingOrder;
use App\Generic\Device\Device;
use App\SplitTest\Activate\ActiveSplitTest;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficSource;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Domain\Settings\DomainSettingsStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Visymo\Shared\Infrastructure\Stub\Domain\Logger\MemoryLoggerStub;

class ConversionLoggerTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private MemoryLoggerStub $memoryLoggerStub;

    private ConversionLogger $conversionLogger;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        /** @var MemoryLoggerStub $memoryLoggerStub */
        $memoryLoggerStub = self::getContainer()->get('test.monolog.logger.conversion');
        $this->memoryLoggerStub = $memoryLoggerStub;

        $activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('ipad')
            ->setClickId(new ClickId('test', ClickIdSource::GOOGLE_CLICK_ID))
            ->setGenericSecondaryClickId(null)
            ->setActiveSplitTest(new ActiveSplitTest(1, 'variant_a'))
            ->setCampaignId(123)
            ->setCampaignName('campaign-name')
            ->setDevice(Device::DESKTOP)
            ->setPublisher('publisher')
            ->setAdditionalChannels(['kwc_123', 'kwc_128'])
            ->setTrafficSource(TrafficSource::GOOGLE)
            ->setNetwork(Network::GOOGLE_SEARCH)
            ->setAccountId(31723)
            ->setAdGroupId(46055)
            ->setKeywordId(59942)
            ->setGoogleLocationId('214132-dfsgsfd-342')
            ->setCustomId(null)
            ->setConversionRoute('route_display_search_related')
            ->create();
        self::getContainer()->set(
            ActiveTrackingEntryHelperInterface::class,
            $activeTrackingEntryHelperStub,
        );

        $websiteSettingsStub = $this->stubs()->websiteSettings();
        $websiteSettingsStub->getGoogleAdSense()->setEnabled(true)
            ->setContractType(ContractType::DIRECT);
        $this->stubs()->domainSettingsHelper()
            ->setSettings(
                (new DomainSettingsStubBuilder())
                    ->setHost('id.brand.com')
                    ->create(),
            );

        $this->stubs()->localeSettingsHelper()
            ->setLocale('en_US');

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setSlug('visymo');
        $brandSettingsStub->setPartnerSlug(null);

        self::websiteSettingsTestHelper()->injectFoodBrandWebsiteConfiguration();
        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        self::brandSettingsTestHelper()->injectBrandSettings($brandSettingsStub);

        /** @var ConversionLogger $conversionLogger */
        $conversionLogger = self::getContainer()->get(ConversionLogger::class);
        $this->conversionLogger = $conversionLogger;
    }

    /**
     * @return array<mixed>
     */
    public static function conversionDataProvider(): array
    {
        return [
            'one per click tracking order'       => [
                'urlParameters'        => [
                    'q'     => 'ipad',
                    'astid' => 123,
                    'acid'  => 456,
                    'arq'   => 'test_arq',
                    'opvid' => '214132-dfsgsfd-342',
                    'vid'   => '1234abcd-1234-1234-1234-abcd12345678',
                    'tv'    => 'clt',
                    'hhc'   => 'https://id.brand.nl',
                ],
                'conversion'           => new Conversion(
                    trackingOrder: new OnePerClickIdTrackingOrder('tracking-order-id'),
                    eventType    : ConversionEventType::CLICK_AD,
                    extraLogData : [
                                       'ad_type'                  => 'google',
                                       'ad_block_number'          => 1,
                                       'ad_number'                => 2,
                                       'ad_unique_ad_click_count' => 3,
                                   ],
                ),
                'googleAdsenseEnabled' => true,
                'expectedLog'          => [
                    'info' => [
                        [
                            'message' => 'conversion_log',
                            'context' => [
                                'event_type'               => 'click_ad',
                                'oid'                      => 'tracking-order-id',
                                'oid_type'                 => 'one_per_click_id',
                                'brand_slug'               => 'visymo',
                                'partner_slug'             => null,
                                'q'                        => 'ipad',
                                'arq'                      => 'test_arq',
                                'pageview_id'              => '214132-dfsgsfd-342',
                                'visit_id'                 => '1234abcd-1234-1234-1234-abcd12345678',
                                'conversion_route'         => 'route_display_search_related',
                                'lander_query'             => 'ipad',
                                'cid'                      => 123,
                                'asid'                     => 'campaign-name',
                                'de'                       => 'c',
                                'traffic_source'           => 'google',
                                'nw'                       => 'g',
                                'ac'                       => 31723,
                                'aid'                      => 46055,
                                'kid'                      => 59942,
                                'clid'                     => 'test',
                                'clid_source'              => 'gclid',
                                'sclid'                    => null,
                                'publisher'                => 'publisher',
                                'lpid'                     => '214132-dfsgsfd-342',
                                'additional_channels'      => ['kwc_123', 'kwc_128'],
                                'traffic_type'             => 'paid',
                                'contract_type'            => 'adsense_direct',
                                'ctid'                     => null,
                                'style_id'                 => 123,
                                'client_id'                => '456',
                                'domain'                   => 'id.brand.com',
                                'locale'                   => 'en_US',
                                'split_test_id'            => 1,
                                'split_test_variant'       => 'variant_a',
                                'template_variant'         => 'clt',
                                'accept_language'          => 'en-us,en;q=0.5',
                                'user_ip'                  => '**************',
                                'country_code'             => 'NL',
                                'http_user_agent'          => [
                                    'full'           => 'Symfony',
                                    'os_family'      => 'Other',
                                    'browser_family' => 'Other',
                                    'device_family'  => 'Other',
                                ],
                                'http_host_client'         => 'https://id.brand.nl',
                                'tls_version'              => '1.2',
                                'ad_type'                  => 'google',
                                'ad_block_number'          => 1,
                                'ad_number'                => 2,
                                'ad_unique_ad_click_count' => 3,
                            ],
                        ],
                    ],
                ],
            ],
            'one per pageview id tracking order' => [
                'urlParameters'        => [
                    'q'     => 'ipad',
                    'astid' => 123,
                    'acid'  => 456,
                    'arq'   => 'test_arq',
                    'opvid' => '214132-dfsgsfd-342',
                    'vid'   => '1234abcd-1234-1234-1234-abcd12345678',
                    'tv'    => 'clt',
                    'hhc'   => 'https://id.brand.nl',
                ],
                'conversion'           => new Conversion(
                    trackingOrder: new OnePerPageviewIdTrackingOrder('tracking-order-id'),
                    eventType    : ConversionEventType::DISPLAY_AD,
                ),
                'googleAdsenseEnabled' => false,
                'expectedLog'          => [
                    'info' => [
                        [
                            'message' => 'conversion_log',
                            'context' => [
                                'event_type'          => 'display_ad',
                                'oid'                 => 'tracking-order-id',
                                'oid_type'            => 'one_per_pageview_id',
                                'brand_slug'          => 'visymo',
                                'partner_slug'        => null,
                                'q'                   => 'ipad',
                                'arq'                 => 'test_arq',
                                'pageview_id'         => '214132-dfsgsfd-342',
                                'visit_id'            => '1234abcd-1234-1234-1234-abcd12345678',
                                'conversion_route'    => 'route_display_search_related',
                                'lander_query'        => 'ipad',
                                'cid'                 => 123,
                                'asid'                => 'campaign-name',
                                'de'                  => 'c',
                                'traffic_source'      => 'google',
                                'nw'                  => 'g',
                                'ac'                  => 31723,
                                'aid'                 => 46055,
                                'kid'                 => 59942,
                                'clid'                => 'test',
                                'clid_source'         => 'gclid',
                                'sclid'               => null,
                                'publisher'           => 'publisher',
                                'lpid'                => '214132-dfsgsfd-342',
                                'additional_channels' => ['kwc_123', 'kwc_128'],
                                'traffic_type'        => 'paid',
                                'contract_type'       => null,
                                'ctid'                => null,
                                'style_id'            => null,
                                'client_id'           => null,
                                'domain'              => 'id.brand.com',
                                'locale'              => 'en_US',
                                'split_test_id'       => 1,
                                'split_test_variant'  => 'variant_a',
                                'template_variant'    => 'clt',
                                'accept_language'     => 'en-us,en;q=0.5',
                                'user_ip'             => '**************',
                                'country_code'        => 'NL',
                                'http_user_agent'     => [
                                    'full'           => 'Symfony',
                                    'os_family'      => 'Other',
                                    'browser_family' => 'Other',
                                    'device_family'  => 'Other',
                                ],
                                'http_host_client'    => 'https://id.brand.nl',
                                'tls_version'         => '1.2',
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @param array<mixed> $urlParameters
     * @param array<mixed> $expectedLog
     */
    #[DataProvider('conversionDataProvider')]
    public function testLogConversion(
        array $urlParameters,
        Conversion $conversion,
        bool $googleAdsenseEnabled,
        array $expectedLog
    ): void
    {
        $request = AbstractBrandWebsitesIntegrationTestCase::createRequest(
            path      : '/',
            parameters: $urlParameters,
            headers   : [
                            'Accept-Language'            => 'en-us,en;q=0.5',
                            'REMOTE_ADDR'                => '**************', // NL
                            'X-Loadbalancer-TLS-Version' => '1.2', // TLS 1.2
                        ],
        );
        $this->setRequest($request);

        $this->stubs()->websiteSettings()
            ->getGoogleAdSense()
            ->setEnabled($googleAdsenseEnabled);

        $this->conversionLogger->logConversion($conversion);

        $actualLog = $this->memoryLoggerStub->getNormalizedLogs();

        static::assertSame($expectedLog, $actualLog);
    }
}
