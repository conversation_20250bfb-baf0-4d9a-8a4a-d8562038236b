<?php

declare(strict_types=1);

namespace Tests\Integration\BingAds\Helper;

use App\Ads\AdTestHelper;
use App\BingAds\Factory\BingAdsFactory;
use App\BingAds\Factory\BingAdsQueryParameter;
use App\BingAds\Factory\BingAdsStyleIdParameter;
use App\BingAds\Helper\BingAdsHelper;
use App\BingAdsStyle\BingAdsStyleHelper;
use App\BingAdsStyle\BingAdsStyleRepository;
use App\Component\Ads\BingAdsAdUnit\BingAdsAdUnitLayout;
use App\Component\Ads\BingAdsBottomAdUnit\BingAdsBottomAdUnitComponent;
use App\Component\Ads\BingAdsSidebarAdUnit\BingAdsSidebarAdUnitComponent;
use App\Component\Ads\BingAdsTopAdUnit\BingAdsTopAdUnitComponent;
use App\ConversionTracking\Endpoint\BingAds\BingAdsConversionUrlGenerator;
use App\Debug\Request\DebugRequestInterface;
use App\GeoIp\GeoIp2CountryHelper;
use App\JsonTemplate\View\DataRequest\BingAdsAdUnitViewDataRequestInterface;
use App\JsonTemplate\View\DataRequest\BingAdsViewDataRequest;
use App\Office\Request\OfficeRequestInterface;
use App\Preferences\Helper\PreferencesHelper;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Tracking\Helper\TrafficHelper;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Locale\Settings\LocaleSettingsHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Visymo\BingAds\AdUnit\AdUnitFactory;
use Visymo\BingAds\BingAds;
use Visymo\BingAds\BingAdsRendererInterface;
use Visymo\BingAds\Clarity\Factory\ClarityFactory;
use Visymo\BingAds\Generic\Property\PropertyHelper;
use Visymo\PhpunitExtensions\PhpUnit\PhpUnitEnv;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;

class BingAdsHelperTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private BingAdsHelper $bingAdsHelper;

    private BingAdsRendererInterface $bingAdsRenderer;

    private PropertyHelper $propertyHelper;

    private LocaleSettingsHelperStub $localeSettingsHelperStub;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();
    }

    private function setUpTestProperties(bool $monetizationEnabled): void
    {
        $this->localeSettingsHelperStub = $this->stubs()
            ->localeSettingsHelper()
            ->setLocale('en_US');

        $this->stubs()
            ->request()
            ->getGenericRequest()
            ->setPageviewId('33ae63a9-2602-4aea-8a09-b6c0790077ad')
            ->setVisitId('7e98d2f7-c630-4f5e-b5b1-4c7c97fd741');

        $this->stubs()
            ->moduleSettings()
            ->getWebSearch()
            ->setEnabled()
            ->setStyleId(1234567890)
            ->create();

        $this->stubs()
            ->moduleSettings()
            ->getTracking()
            ->setCampaignNameValidationEnabled(false)
            ->create();

        $monetizationSettings = $this->stubs()
            ->moduleSettings()
            ->getMonetization()
            ->setEnabled($monetizationEnabled)
            ->create();

        /** @var DateTimeFactoryStub $dateTimeFactoryStub */
        $dateTimeFactoryStub = self::getContainer()->get(DateTimeFactory::class);

        // Set fixed datetime
        $fixedDateTime = $dateTimeFactoryStub->create('2022-01-10 09:00:00', TimezoneEnum::UTC);
        $dateTimeFactoryStub->setDateTime($fixedDateTime);

        /** @var SearchRequestInterface $searchRequest */
        $searchRequest = self::getContainer()->get(SearchRequestInterface::class);

        /** @var DebugRequestInterface $debugRequest */
        $debugRequest = self::getContainer()->get(DebugRequestInterface::class);

        $websiteSettingsStub = new WebsiteSettingsStub();
        $websiteSettingsStub->getBingAds()
            ->setEnabled(true)
            ->setSemAdUnitId('123')
            ->setWebAdUnitId('456')
            ->setClarityId('my_clarity_id');

        /** @var WebsiteSettingsHelperStub $websiteSettingsHelperStub */
        $websiteSettingsHelperStub = self::getContainer()->get(WebsiteSettingsHelperStub::class);
        $websiteSettingsHelperStub->setWebsiteSettings($websiteSettingsStub);

        /** @var MockObject|PreferencesHelper $preferencesHelperMock */
        $preferencesHelperMock = $this->createMock(PreferencesHelper::class);

        /** @var SplitTestExtendedReaderInterface $splitTestExtendedReader */
        $splitTestExtendedReader = self::getContainer()->get(SplitTestExtendedReaderInterface::class);

        /** @var GeoIp2CountryHelper $geoIpCountryHelper */
        $geoIpCountryHelper = self::getContainer()->get(GeoIp2CountryHelper::class);

        /** @var MockObject|TrafficHelper $trafficHelperMock */
        $trafficHelperMock = $this->createConfiguredMock(
            TrafficHelper::class,
            [
                'getTrackingChannel' => 'test_channel',
            ],
        );

        /** @var BingAdsQueryParameter $bingAdsQueryParameter */
        $bingAdsQueryParameter = self::getContainer()->get(BingAdsQueryParameter::class);

        /** @var BingAdsStyleRepository $bingAdsStyleRepository */
        $bingAdsStyleRepository = self::getContainer()->get(BingAdsStyleRepository::class);

        /** @var BingAdsStyleHelper $bingAdsStyleHelper */
        $bingAdsStyleHelper = self::getContainer()->get(BingAdsStyleHelper::class);

        /** @var BingAdsStyleIdParameter $bingAdsStyleIdParameter */
        $bingAdsStyleIdParameter = self::getContainer()->get(BingAdsStyleIdParameter::class);

        /** @var BingAdsConversionUrlGenerator $bingAdsConversionUrlGenerator */
        $bingAdsConversionUrlGenerator = self::getContainer()->get(BingAdsConversionUrlGenerator::class);

        /** @var AdTestHelper $adTestHelper */
        $adTestHelper = self::getContainer()->get(AdTestHelper::class);

        /** @var ClarityFactory $clarityFactory */
        $clarityFactory = self::getContainer()->get(ClarityFactory::class);

        /** @var LoggerInterface $logger */
        $logger = self::getContainer()->get(LoggerInterface::class);

        /** @var TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker */
        $trademarkInfringementResultBlocker = self::getContainer()->get(TrademarkInfringementResultBlocker::class);

        $bingAdsFactory = new BingAdsFactory(
            searchRequest                     : $searchRequest,
            debugRequest                      : $debugRequest,
            websiteSettingsHelper             : $websiteSettingsHelperStub,
            preferencesHelper                 : $preferencesHelperMock,
            splitTestExtendedReader           : $splitTestExtendedReader,
            geoIpCountryHelper                : $geoIpCountryHelper,
            trafficHelper                     : $trafficHelperMock,
            bingAdsQueryParameter             : $bingAdsQueryParameter,
            bingAdsStyleRepository            : $bingAdsStyleRepository,
            bingAdsStyleHelper                : $bingAdsStyleHelper,
            bingAdsStyleIdParameter           : $bingAdsStyleIdParameter,
            bingAdsConversionUrlGenerator     : $bingAdsConversionUrlGenerator,
            adTestHelper                      : $adTestHelper,
            clarityFactory                    : $clarityFactory,
            logger                            : $logger,
            trademarkInfringementResultBlocker: $trademarkInfringementResultBlocker,
            localeSettingsHelper              : $this->localeSettingsHelperStub,
            monetizationSettings              : $monetizationSettings,
        );

        $this->bingAdsHelper = new BingAdsHelper(
            bingAdsFactory: $bingAdsFactory,
            adUnitFactory : new AdUnitFactory(),
        );

        /** @var BingAdsRendererInterface $bingAdsRenderer */
        $bingAdsRenderer = self::getContainer()->get(BingAdsRendererInterface::class);
        $this->bingAdsRenderer = $bingAdsRenderer;

        /** @var PropertyHelper $propertyHelper */
        $propertyHelper = self::getContainer()->get(PropertyHelper::class);
        $this->propertyHelper = $propertyHelper;
    }

    /**
     * @return mixed[]
     */
    public static function initFromViewDataRequestDataProvider(): array
    {
        return [
            'without merge'            => [
                'adUnits'             => [
                    new BingAdsTopAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 5),
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                    new BingAdsSidebarAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'mergeAdUnits'        => [],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY             => 'pizza',
                    DebugRequestInterface::PARAMETER_DEBUG_COUNTRY_CODE => 'US',
                ],
                'locale'              => 'nl_NL',
                'path'                => '/ws',
                'monetizationEnabled' => true,
                'expectedEmpty'       => false,
                'expectedException'   => null,
            ],
            'merge landingpage'        => [
                'adUnits'             => [
                    new BingAdsTopAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 5),
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'mergeAdUnits'        => [
                    new BingAdsSidebarAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 2),
                ],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY               => 'pizza',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_STYLE_ID => 2,
                    SearchRequestInterface::PARAMETER_PAGE                => 2,
                ],
                'locale'              => 'de_DE',
                'path'                => '/ws',
                'monetizationEnabled' => true,
                'expectedEmpty'       => false,
                'expectedException'   => null,
            ],
            'merge advertised'         => [
                'adUnits'             => [
                    new BingAdsTopAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 5),
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'mergeAdUnits'        => [
                    new BingAdsSidebarAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 2),
                ],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY               => 'pizza',
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_STYLE_ID => 2,
                    SearchRequestInterface::PARAMETER_PAGE                => 2,
                ],
                'locale'              => 'de_DE',
                'path'                => '/wsa',
                'monetizationEnabled' => true,
                'expectedEmpty'       => false,
                'expectedException'   => null,
            ],
            'merge exception'          => [
                'adUnits'             => [
                    new BingAdsTopAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 5),
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'mergeAdUnits'        => [
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY => 'pizza',
                ],
                'locale'              => 'nl_NL',
                'path'                => '/ws',
                'monetizationEnabled' => true,
                'expectedEmpty'       => true,
                'expectedException'   => \InvalidArgumentException::class,
            ],
            'empty'                    => [
                'adUnits'             => [],
                'mergeAdUnits'        => [],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY => 'pizza',

                ],
                'locale'              => 'nl_NL',
                'path'                => '/ws',
                'monetizationEnabled' => true,
                'expectedEmpty'       => true,
                'expectedException'   => null,
            ],
            'disabled by debug'        => [
                'adUnits'             => [
                    new BingAdsTopAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 5),
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'mergeAdUnits'        => [],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY                 => 'pizza',
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_BING_ADS => 1,
                ],
                'locale'              => 'nl_NL',
                'path'                => '/ws',
                'monetizationEnabled' => true,
                'expectedEmpty'       => true,
                'expectedException'   => null,
            ],
            'disabled by monetization' => [
                'adUnits'             => [
                    new BingAdsTopAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 5),
                    new BingAdsBottomAdUnitComponent(BingAdsAdUnitLayout::DEFAULT, 3),
                ],
                'mergeAdUnits'        => [],
                'urlParameters'       => [
                    SearchRequestInterface::PARAMETER_QUERY => 'pizza',
                ],
                'locale'              => 'nl_NL',
                'path'                => '/ws',
                'monetizationEnabled' => false,
                'expectedEmpty'       => true,
                'expectedException'   => null,
            ],
        ];
    }

    /**
     * @param BingAdsAdUnitViewDataRequestInterface[] $adUnits
     * @param BingAdsAdUnitViewDataRequestInterface[] $mergeAdUnits
     * @param mixed[]                                 $urlParameters
     */
    #[DataProvider('initFromViewDataRequestDataProvider')]
    public function testInitFromViewDataRequest(
        array $adUnits,
        array $mergeAdUnits,
        array $urlParameters,
        string $locale,
        string $path,
        bool $monetizationEnabled,
        bool $expectedEmpty,
        ?string $expectedException
    ): void
    {
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $this->setUpTestProperties($monetizationEnabled);

        $this->localeSettingsHelperStub
            ->setLocale($locale);

        $request = Request::create(
            sprintf(
                'https://www.visymo.com%s?%s',
                $path,
                http_build_query($urlParameters),
            ),
        );
        $request->headers->set(OfficeRequestInterface::HEADER_X_LOADBALANCER_IS_OFFICE, '1');
        $routeInfo = $this->getRouteInfoFromRequest($request);
        $request->attributes->add($routeInfo);
        $this->setRequest($request);

        /** @var RouteRegistry $routeRegistry */
        $routeRegistry = self::getContainer()->get(RouteRegistry::class);
        $routeRegistry->setSearchRoute('route_web_search_advertised');

        $bingAdsViewDataRequest = $this->createBingAdsViewDataRequest($adUnits);

        if ($mergeAdUnits !== []) {
            $bingAdsViewDataRequest->mergeWith(
                [
                    $this->createBingAdsViewDataRequest($mergeAdUnits),
                ],
            );
        }

        $this->bingAdsHelper->initFromViewDataRequest($bingAdsViewDataRequest);

        if ($expectedEmpty) {
            self::assertNull($this->bingAdsHelper->getBingAds());
            self::assertFalse($this->bingAdsHelper->hasUnits());

            $actualContent = '';
        } else {
            /** @var BingAds $bingAds */
            $bingAds = $this->bingAdsHelper->getBingAds();
            $properties = $this->propertyHelper->excludeNullValuesOfProperties(
                $bingAds->getPageOptions()->getProperties(),
            );

            $actualOutput = [
                $this->bingAdsRenderer->getScriptHtml($bingAds),
                sprintf('<script>%s</script>', $this->bingAdsRenderer->getJavaScript($bingAds)),
            ];
            $actualOutput[] = $this->propertyHelper->parsePropertiesToJson($properties);
            $actualOutput[] = json_encode(
                $this->propertyHelper->parsePropertiesToArray($properties),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT,
            );

            $actualContent = implode(PHP_EOL, $actualOutput).PHP_EOL;
        }

        $fileName = str_replace(' ', '_', (string)$this->dataName());
        $expectedResponseFile = sprintf('%s/_assertions/%s.txt', __DIR__, $fileName);

        if (PhpUnitEnv::isUpdateAssertions()) {
            file_put_contents($expectedResponseFile, $actualContent);

            self::markTestIncomplete('Updated expected responses');
        } else {
            self::assertStringEqualsFile($expectedResponseFile, $actualContent);
        }
    }

    /**
     * @param BingAdsAdUnitViewDataRequestInterface[] $adUnits
     */
    private function createBingAdsViewDataRequest(array $adUnits): BingAdsViewDataRequest
    {
        $bingAdsViewDataRequest = new BingAdsViewDataRequest();

        foreach ($adUnits as $adUnit) {
            $bingAdsViewDataRequest->addAdUnit($adUnit);
        }

        return $bingAdsViewDataRequest;
    }
}
