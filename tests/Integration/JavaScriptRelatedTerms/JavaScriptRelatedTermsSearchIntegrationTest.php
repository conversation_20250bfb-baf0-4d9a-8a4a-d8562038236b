<?php

declare(strict_types=1);

namespace Tests\Integration\JavaScriptRelatedTerms;

use App\Account\Settings\AccountSettings;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Integration\WebsiteSettings\AbstractWebsiteSettingsTestCase;
use Tests\Stub\Assets\AssetsHelperStub;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;

class JavaScriptRelatedTermsSearchIntegrationTest extends AbstractWebsiteSettingsTestCase
{
    private AssetsHelperStub $assetsHelperStub;

    private WebsiteConfigurationHelper $websiteConfigurationHelper;

    private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->stubs()->localeSettingsHelper()
            ->setLocale('nl_NL');

        /** @var AssetsHelperStub $assetsHelperStub */
        $assetsHelperStub = self::getContainer()->get(AssetsHelperStub::class);
        $this->assetsHelperStub = $assetsHelperStub;

        /** @var ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper */
        $activeTrackingEntryHelper = self::getContainer()->get(ActiveTrackingEntryHelperInterface::class);
        $this->activeTrackingEntryHelper = $activeTrackingEntryHelper;

        $javaScriptRelatedTermsSettings = $this->stubs()->moduleSettings()->getJavaScriptRelatedTerms();
        $javaScriptRelatedTermsSettings->setEnabledForSearch(true);
        $javaScriptRelatedTermsSettings->create();

        $this->stubs()->request()
            ->getGenericRequest()
            ->setPageviewId('pageview_id');

        /** @var WebsiteConfigurationHelper $websiteConfigurationHelper */
        $websiteConfigurationHelper = self::getContainer()->get(WebsiteConfigurationHelper::class);
        $this->websiteConfigurationHelper = $websiteConfigurationHelper;
    }

    /**
     * @return mixed[]
     */
    public static function endpointResponseDataProvider(): array
    {
        return [
            'with gclid'                     => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'q'     => 'Hotel Disney',
                        'asid'  => 'cmp_123_g',
                        'gclid' => 'gclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'query parameter missing'        => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'asid'  => 'cmp_123_g',
                        'gclid' => 'gclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'with msclkid'                   => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'q'       => 'Hotel Disney',
                        'asid'    => 'cmp_123_ms',
                        'msclkid' => 'msclkid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'with fbclid'                    => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'q'      => 'Hotel Disney',
                        'asid'   => 'cmp_123_fb',
                        'fbclid' => 'fbclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'with generic click ids'         => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'q'     => 'Hotel Disney',
                        'asid'  => 'cmp_123_gen',
                        'clid'  => 'clid123123',
                        'sclid' => 'sclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'remove specific url parameters' => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'asid' => 'cmp_123_abc',
                        'q'    => 'Hotel Disney',
                        'pg'   => '2',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'valid style id'                 => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'q'        => 'Hotel Disney',
                        'asid'     => 'cmp_123_abc',
                        'style_id' => '87654321',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
            'invalid style id is ignored'    => [
                'request'                    => AbstractBrandWebsitesIntegrationTestCase::createRequest(
                    '/p/related-terms-search/v1',
                    [
                        'q'        => 'Hotel Disney',
                        'asid'     => 'cmp_123_abc',
                        'style_id' => '8765aaa4321',
                    ],
                ),
                'javaScriptContentAvailable' => true,
            ],
        ];
    }

    #[DataProvider('endpointResponseDataProvider')]
    public function testEndpointResponse(
        Request $request,
        bool $javaScriptContentAvailable
    ): void
    {
        // inject expected settings
        $websiteSettingsStub = new WebsiteSettingsStub();
        $websiteSettingsStub->getGoogleAdSense()
            ->setEnabled(true)
            ->setDefaultClient('default_client')
            ->setSemClient('sem_client')
            ->setWebClient('web_client');

        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);

        if ($javaScriptContentAvailable) {
            $this->assetsHelperStub->setJavaScriptEntryFileContents(
                'JavaScriptRelatedTerms',
                (string)file_get_contents(sprintf('%s/_assets/dummy_entry.js', __DIR__)),
            );
        } else {
            $this->assetsHelperStub->setJavaScriptEntryFileContents(
                'JavaScriptRelatedTerms',
                null,
            );
        }

        $trackingEntryStubBuilder = new TrackingEntryStubBuilder();
        $trackingEntryStubBuilder->setQuery($request->query->getString('q'));
        $trackingEntryStubBuilder->setAccountId(1);
        $trackingEntryStubBuilder->setCampaignName($request->query->getString('asid'));

        $this->activeTrackingEntryHelper->setActiveTrackingEntry($trackingEntryStubBuilder->create());

        $foodBrandConfig = $this->brandConfigTestHelper->getFoodBrandConfig();
        $websiteConfiguration = new WebsiteConfiguration(
            [
                ...$foodBrandConfig,
                WebsiteConfiguration::KEY_ACCOUNTS => [
                    1 => [
                        AccountSettings::KEY_CAMPAIGNS => [
                            $request->query->getString('asid') => [
                                AccountSettings::KEY_SERVICE => 'service',
                            ],
                        ],
                    ],
                ],
            ],
        );

        $this->websiteConfigurationHelper->setConfiguration($websiteConfiguration);

        // handle request
        $response = $this->handleRequest($request);
        $actualContent = (string)$response->getContent();
        $actualContent = preg_replace(
            '/https:(.*)\/(p|dsrw|js-error)/',
            'https://localhost\/$2',
            $actualContent,
        );
        $actualContent = preg_replace('/(&?ste=[^&]*)",/U', '",', (string)$actualContent);
        $actualContent = preg_replace('/(&?ste=[^&]*)/', '', (string)$actualContent);

        self::assertSame(
            Response::HTTP_OK,
            $response->getStatusCode(),
            'Endpoint status code when module is enabled',
        );

        $assertionFile = $this->initGenericAssertionFile((string)$actualContent, 'js');
        $assertionFile->assertSame();
    }
}
