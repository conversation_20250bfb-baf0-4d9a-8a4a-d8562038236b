<?php

declare(strict_types=1);

namespace Tests\Integration\ContentPage\OpenGraph;

use App\ContentPage\Exception\NoAvailableContentPageUrlProviderFoundException;
use App\ContentPage\OpenGraph\ContentPageToOpenGraphGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\ModuleSettings\ModuleSettingsStubs;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageCategory;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageImage;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageMeta;

final class ContentPageToOpenGraphGeneratorTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private ContentPageToOpenGraphGenerator $contentPageToOpenGraphGenerator;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $moduleSettingsStub = $this->stubs()->moduleSettings();
        $moduleSettingsStub->getArticle()->setEnabled(false);
        $moduleSettingsStub->getContentSearch()->setEnabled(false);
        $moduleSettingsStub->getDisplaySearchRelated()->setEnabled(false);
        $moduleSettingsStub->getMicrosoftSearchRelated()->setEnabled(false);

        $websiteSettingsStub = $this->stubs()->websiteSettings();

        $this->stubs()->localeSettingsHelper()
            ->setLocale('en_US');

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setName('Visymo');

        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        self::brandSettingsTestHelper()->injectBrandSettings($brandSettingsStub);

        /** @var ContentPageToOpenGraphGenerator $contentPageToOpenGraphGenerator */
        $contentPageToOpenGraphGenerator = self::getContainer()->get(ContentPageToOpenGraphGenerator::class);
        $this->contentPageToOpenGraphGenerator = $contentPageToOpenGraphGenerator;
    }

    /**
     * @return mixed[]
     */
    public static function generateOpenGraphDataProvider(): array
    {
        return [
            'article module'                  => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getArticle()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'display search related module'   => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getDisplaySearchRelated()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'microsoft search related module' => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getMicrosoftSearchRelated()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'none'                            => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getArticle()->setEnabled(false);
                    $moduleSettingsStubs->getDisplaySearchRelated()->setEnabled(false);
                    $moduleSettingsStubs->getMicrosoftSearchRelated()->setEnabled(false);
                },
                'expectedException'           => NoAvailableContentPageUrlProviderFoundException::class,
            ],
        ];
    }

    #[DataProvider('generateOpenGraphDataProvider')]
    public function testGenerateOpenGraph(
        callable $moduleSettingsStubsCallback,
        ?string $expectedException
    ): void
    {
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $moduleSettingsStubsCallback($this->stubs()->moduleSettings());
        $this->handleModuleSettingsStubs();

        $request = Request::create('/');
        $this->handleRequestRoute($request);
        $this->setRequest($request);

        $openGraph = $this->contentPageToOpenGraphGenerator->generate(
            $this->createContentPage(),
        );

        $assertionFile = $this->initJsonAssertionFile($openGraph->toArray());
        $assertionFile->assertSame();
    }

    /**
     * @return mixed[]
     */
    public static function generateOpenGraphDataWithRouteProvider(): array
    {
        return [
            'article module'                  => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getArticle()->setEnabled(true);
                },
                'preferenceRoute'             => 'route_article',
                'expectedException'           => null,
            ],
            'content search module'           => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getContentSearch()->setEnabled(true);
                },
                'preferenceRoute'             => 'route_content_search',
                'expectedException'           => null,
            ],
            'display search related module'   => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getDisplaySearchRelated()->setEnabled(true);
                },
                'preferenceRoute'             => 'route_display_search_related',
                'expectedException'           => null,
            ],
            'microsoft search related module' => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getMicrosoftSearchRelated()->setEnabled(true);
                },
                'preferenceRoute'             => 'route_microsoft_search_related',
                'expectedException'           => null,
            ],
            'none'                            => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getArticle()->setEnabled(false);
                    $moduleSettingsStubs->getDisplaySearchRelated()->setEnabled(false);
                    $moduleSettingsStubs->getMicrosoftSearchRelated()->setEnabled(false);
                },
                'preferenceRoute'             => null,
                'expectedException'           => NoAvailableContentPageUrlProviderFoundException::class,
            ],
        ];
    }

    #[DataProvider('generateOpenGraphDataWithRouteProvider')]
    public function testGenerateOpenGraphWithRoute(
        callable $moduleSettingsStubsCallback,
        ?string $preferenceRoute,
        ?string $expectedException
    ): void
    {
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $moduleSettingsStubsCallback($this->stubs()->moduleSettings());
        $this->handleModuleSettingsStubs();

        $request = Request::create('/');

        if ($preferenceRoute !== null) {
            $request->attributes->set('_route', $preferenceRoute);
        } else {
            $this->handleRequestRoute($request);
        }

        $this->setRequest($request);

        $openGraph = $this->contentPageToOpenGraphGenerator->generate(
            $this->createContentPage(),
        );

        $assertionFile = $this->initJsonAssertionFile($openGraph->toArray());
        $assertionFile->assertSame();
    }

    private function createContentPage(): ContentPage
    {
        $contentPageCategory = new ContentPageCategory(
            id                     : 165,
            publicId               : 543,
            locale                 : 'en_US',
            title                  : 'Category name',
            slug                   : 'category-slug',
            level                  : 0,
            parentCategoryPublicId : null,
            parentCategoryPublicIds: [],
            isAdult                : false,
        );
        $contentPageMeta = ContentPageMeta::create(
            keywords   : 'hello world, hello jupiter',
            description: 'Description',
        );
        $contentPageImage = ContentPageImage::create(
            id      : 985,
            publicId: 2543543674,
            title   : 'Image title',
            slug    : 'image-slug',
            path    : 'image-path',
        );

        return ContentPage::create(
            id                  : 7655,
            publicId            : 58987,
            locale              : 'en_US',
            collectionSlug      : 'default',
            isHomepage          : true,
            isFallbackLocale    : false,
            isFallbackCollection: false,
            title               : 'Title',
            slug                : 'slug',
            category            : $contentPageCategory,
            excerpt             : 'excerpt',
            readingTime         : 5,
            publishedAt         : null,
            meta                : $contentPageMeta,
            image               : $contentPageImage,
            paragraphs          : [],
            keywords            : [
                                      'content page keyword 1',
                                      'content page keyword 2',
                                  ],
        );
    }

    private function handleModuleSettingsStubs(): void
    {
        $moduleSettingsStub = $this->stubs()->moduleSettings();
        $moduleSettingsStub->getArticle()->create();
        $moduleSettingsStub->getContentSearch()->create();
        $moduleSettingsStub->getDisplaySearchRelated()->create();
        $moduleSettingsStub->getMicrosoftSearchRelated()->create();
    }
}
