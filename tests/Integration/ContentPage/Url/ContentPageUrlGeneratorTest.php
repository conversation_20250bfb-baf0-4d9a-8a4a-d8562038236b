<?php

declare(strict_types=1);

namespace Tests\Integration\ContentPage\Url;

use App\ContentPage\Exception\NoAvailableContentPageUrlProviderFoundException;
use App\ContentPage\Url\ContentPageUrlGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\ModuleSettings\ModuleSettingsStubs;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

final class ContentPageUrlGeneratorTest extends AbstractBrandWebsitesIntegrationTestCase
{
    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $moduleSettingsStub = $this->stubs()->moduleSettings();
        $moduleSettingsStub->getArticle()->setEnabled(false);
        $moduleSettingsStub->getContentSearch()->setEnabled(false);
        $moduleSettingsStub->getDisplaySearchRelated()->setEnabled(false);
        $moduleSettingsStub->getMicrosoftSearchRelated()->setEnabled(false);
        $moduleSettingsStub->getSearch()->setEnabled()
            ->setStyleId(**********);
    }

    /**
     * @return mixed[]
     */
    public static function generateDataProvider(): array
    {
        return [
            'article module'                  => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getArticle()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'content search module'           => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getContentSearch()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'display search related module'   => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getDisplaySearchRelated()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'microsoft search related module' => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getMicrosoftSearchRelated()->setEnabled(true);
                },
                'expectedException'           => null,
            ],
            'none'                            => [
                'moduleSettingsStubsCallback' => static function (ModuleSettingsStubs $moduleSettingsStubs): void {
                    $moduleSettingsStubs->getArticle()->setEnabled(false);
                    $moduleSettingsStubs->getContentSearch()->setEnabled(false);
                    $moduleSettingsStubs->getDisplaySearchRelated()->setEnabled(false);
                    $moduleSettingsStubs->getMicrosoftSearchRelated()->setEnabled(false);
                },
                'expectedException'           => NoAvailableContentPageUrlProviderFoundException::class,
            ],
        ];
    }

    #[DataProvider('generateDataProvider')]
    public function testGeneratePersistentUrl(
        callable $moduleSettingsStubsCallback,
        ?string $expectedException
    ): void
    {
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $moduleSettingsStubsCallback($this->stubs()->moduleSettings());
        $this->handleModuleSettingsStubs();

        $request = self::createRequest('/');
        $this->handleRequest($request);

        $contentPage = self::createContentPage();
        $url = $this->getContentPageUrlGenerator()->generatePersistentUrl($contentPage);

        self::assertTrue(str_starts_with($url, '/'));
    }

    #[DataProvider('generateDataProvider')]
    public function testGenerateUrl(
        callable $moduleSettingsStubsCallback,
        ?string $expectedException
    ): void
    {
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $moduleSettingsStubsCallback($this->stubs()->moduleSettings());
        $this->handleModuleSettingsStubs();

        $request = self::createRequest('/');
        $this->handleRequest($request);

        $contentPage = self::createContentPage();
        $url = $this->getContentPageUrlGenerator()->generateUrl(
            contentPage: $contentPage,
            absoluteUrl: true,
        );

        self::assertTrue(str_starts_with($url, 'https://'));
    }

    private static function createContentPage(): ContentPage
    {
        return ContentPage::create(
            id                  : 1,
            publicId            : 1234,
            locale              : 'en_US',
            collectionSlug      : 'default',
            isHomepage          : false,
            isFallbackLocale    : false,
            isFallbackCollection: false,
            title               : 'Title',
            slug                : 'my-title',
            category            : null,
            excerpt             : 'Excerpt',
            readingTime         : 1,
            publishedAt         : null,
            meta                : null,
            image               : null,
            paragraphs          : [],
            keywords            : [],
        );
    }

    private function getContentPageUrlGenerator(): ContentPageUrlGenerator
    {
        /** @var ContentPageUrlGenerator $contentPageUrlGenerator */
        $contentPageUrlGenerator = self::getContainer()->get(ContentPageUrlGenerator::class);

        return $contentPageUrlGenerator;
    }

    private function handleModuleSettingsStubs(): void
    {
        $moduleSettingsStub = $this->stubs()->moduleSettings();
        $moduleSettingsStub->getArticle()->create();
        $moduleSettingsStub->getContentSearch()->create();
        $moduleSettingsStub->getDisplaySearchRelated()->create();
        $moduleSettingsStub->getMicrosoftSearchRelated()->create();
        $moduleSettingsStub->getSearch()->create();
    }
}
