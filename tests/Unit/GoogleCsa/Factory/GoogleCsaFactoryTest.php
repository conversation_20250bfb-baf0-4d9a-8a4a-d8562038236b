<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Factory;

use App\Ads\AdTestHelper;
use App\Generic\Url\UrlHelper;
use App\GoogleCsa\Factory\GoogleCsaFactory;
use App\GoogleCsa\Helper\GoogleCsaClickTrackUrlHelper;
use App\SplitTest\SplitTestExtended;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Stub\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseConversionUrlGeneratorStub;
use Tests\Stub\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsConversionUrlGeneratorStub;
use Tests\Stub\GoogleCsa\Parameter\GoogleCsaIgnorePageParameterStub;
use Tests\Stub\GoogleCsa\Parameter\GoogleCsaPublisherIdParameterStub;
use Tests\Stub\GoogleCsa\Parameter\GoogleCsaRelatedTermsResultsPageBaseUrlParameterStub;
use Tests\Stub\GoogleCsa\StyleId\GoogleCsaStyleIdParameterStub;
use Tests\Stub\Locale\Settings\LocaleSettingsHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Tests\Unit\GoogleCsa\Factory\GoogleCsaFactoryTest\AbstractGoogleCsaFactoryCase;
use Visymo\GoogleCsa\Ads\Unit\AdUnitFactory;
use Visymo\GoogleCsa\GoogleCsaRenderer;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\TestCaseDataProviderHelpers;

final class GoogleCsaFactoryTest extends PhpUnitTestCase
{
    private GoogleCsaFactory $googleCsaFactory;

    private GoogleCsaRenderer $googleCsaRenderer;

    protected function setUp(): void
    {
        parent::setUp();

        $websiteSettingsStub = new WebsiteSettingsStub();

        $websiteSettingsHelperMock = $this->createConfiguredMock(
            WebsiteSettingsHelper::class,
            [
                'getSettings' => $websiteSettingsStub,
            ],
        );

        $googleCsaPublisherIdParameterStub = new GoogleCsaPublisherIdParameterStub(
            'visymo',
        );
        $googleCsaStyleIdParameterStub = new GoogleCsaStyleIdParameterStub();
        $googleCsaStyleIdParameterStub->setStyleId(**********);

        $googleCsaRelatedTermsResultsPageBaseUrlParameterStub = new GoogleCsaRelatedTermsResultsPageBaseUrlParameterStub();
        $googleCsaRelatedTermsResultsPageBaseUrlParameterStub
            ->setResultsPageBaseUrl('https://www.visymo.com/dsrw');

        $googleAdSenseConversionUrlGeneratorStub = new GoogleAdSenseConversionUrlGeneratorStub();
        $googleAdSenseConversionUrlGeneratorStub->setUrl('https://www.visymo.com');

        $googleRelatedTermsConversionUrlGeneratorStub = new GoogleRelatedTermsConversionUrlGeneratorStub();
        $googleRelatedTermsConversionUrlGeneratorStub->setUrl('https://www.visymo.com');

        $googleCsaIgnorePageParameterStub = new GoogleCsaIgnorePageParameterStub();
        $googleCsaIgnorePageParameterStub->setIgnoreParams(['ac', 'asid', 'p']);

        $localeSettingsHelper = (new LocaleSettingsHelperStub())
            ->setLocale('nl_NL');

        $this->googleCsaFactory = new GoogleCsaFactory(
            $localeSettingsHelper,
            new SplitTestExtended(),
            $googleCsaPublisherIdParameterStub,
            $googleCsaStyleIdParameterStub,
            $googleCsaRelatedTermsResultsPageBaseUrlParameterStub,
            $googleCsaIgnorePageParameterStub,
            $googleAdSenseConversionUrlGeneratorStub,
            $googleRelatedTermsConversionUrlGeneratorStub,
            new AdUnitFactory(),
            new AdTestHelper(true, $websiteSettingsHelperMock),
            new GoogleCsaClickTrackUrlHelper(
                new UrlHelper(),
            ),
        );
        $this->googleCsaRenderer = new GoogleCsaRenderer();
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return TestCaseDataProviderHelpers::initDataProviderCases(
            __DIR__.'/GoogleCsaFactoryTest',
            self::class,
        );
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(AbstractGoogleCsaFactoryCase $testCase): void
    {
        $googleCsa = $this->googleCsaFactory->create($testCase->getRequest());
        $actualContent = $googleCsa !== null
            ? $this->googleCsaRenderer->getJavaScript($googleCsa, true)
            : 'No GoogleCSA';

        $assertionFile = $this->initGenericAssertionFile(
            $actualContent,
            'js',
            $testCase->getAssertionFilePath('js'),
        );
        $assertionFile->assertSame();
    }
}
