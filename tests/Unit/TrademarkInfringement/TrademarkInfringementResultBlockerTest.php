<?php

declare(strict_types=1);

namespace Tests\Unit\TrademarkInfringement;

use App\Search\Query\SearchQueryNormalizer;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use App\TrademarkInfringement\TrademarkInfringementRuleMatcher;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Locale\Settings\LocaleSettingsHelperStub;
use Tests\Stub\Project\Config\ProjectConfigRegistryStub;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;

final class TrademarkInfringementResultBlockerTest extends TestCase
{
    private SearchRequestStub $searchRequestStub;

    private SeaRequestStub $seaRequestStub;

    private TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker;

    private LocaleSettingsHelperStub $localeSettingsHelperStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->searchRequestStub = new SearchRequestStub();
        $this->seaRequestStub = new SeaRequestStub();

        $this->localeSettingsHelperStub = (new LocaleSettingsHelperStub())
            ->setLocale('en_US');

        $trademarkInfringementRuleMatcher = new TrademarkInfringementRuleMatcher(
            searchRequest        : $this->searchRequestStub,
            searchQueryNormalizer: new SearchQueryNormalizer(),
            seaRequest           : $this->seaRequestStub,
            localeSettingsHelper : $this->localeSettingsHelperStub,
        );

        $projectConfigRegistryStub = new ProjectConfigRegistryStub();
        $projectConfigRegistryStub->setProjectConfigFromJsonFile(
            __DIR__.'/data/brand-websites.json',
        );

        $this->trademarkInfringementResultBlocker = new TrademarkInfringementResultBlocker(
            trademarkInfringementRuleMatcher: $trademarkInfringementRuleMatcher,
            projectConfigRegistry           : $projectConfigRegistryStub,
        );
    }

    /**
     * @return mixed[]
     */
    public static function blockResultsDataProvider(): array
    {
        return [
            'no query'                                        => [
                'searchQuery'          => null,
                'racQuery'             => null,
                'locale'               => 'nl_NL',
                'expectedBlockResults' => false,
            ],
            'no match'                                        => [
                'searchQuery'          => 'buy ipad air',
                'racQuery'             => null,
                'locale'               => 'en_US',
                'expectedBlockResults' => false,
            ],
            'phrase match, locale irrelevant'                 => [
                'searchQuery'          => 'leroy merlin store',
                'racQuery'             => null,
                'locale'               => 'nl_NL',
                'expectedBlockResults' => true,
            ],
            'phrase match, locale irrelevant, glued query'    => [
                'searchQuery'          => 'leroymerlin store',
                'racQuery'             => null,
                'locale'               => 'nl_NL',
                'expectedBlockResults' => true,
            ],
            'phrase match, locale relevant, case insensitive' => [
                'searchQuery'          => 'ultra epoXy Clear store',
                'racQuery'             => null,
                'locale'               => 'en_US',
                'expectedBlockResults' => true,
            ],
            'phrase no match, locale relevant'                => [
                'searchQuery'          => 'ultra epoxy clear store',
                'racQuery'             => null,
                'locale'               => 'de_DE',
                'expectedBlockResults' => false,
            ],
            'exact match, locale irrelevant'                  => [
                'searchQuery'          => 'purple',
                'racQuery'             => null,
                'locale'               => 'es_ES',
                'expectedBlockResults' => true,
            ],
            'exact match, locale relevant'                    => [
                'searchQuery'          => 'paars rood',
                'racQuery'             => null,
                'locale'               => 'nl_NL',
                'expectedBlockResults' => true,
            ],
            'exact match, locale relevant, glued query'       => [
                'searchQuery'          => 'paarsrood',
                'racQuery'             => null,
                'locale'               => 'nl_NL',
                'expectedBlockResults' => true,
            ],
            'exact no match, locale relevant'                 => [
                'searchQuery'          => 'paars rood',
                'racQuery'             => null,
                'locale'               => 'es_ES',
                'expectedBlockResults' => false,
            ],
            'rac query match'                                 => [
                'searchQuery'          => null,
                'racQuery'             => 'Hello Leroy Merlin',
                'locale'               => 'en_US',
                'expectedBlockResults' => true,
            ],
        ];
    }

    #[DataProvider('blockResultsDataProvider')]
    public function testBlockResults(
        ?string $searchQuery,
        ?string $racQuery,
        string $locale,
        bool $expectedBlockResults
    ): void
    {
        $this->searchRequestStub->setQuery($searchQuery);
        $this->seaRequestStub->setReferrerAdCreative($racQuery);
        $this->localeSettingsHelperStub
            ->setLocale($locale);

        self::assertSame($expectedBlockResults, $this->trademarkInfringementResultBlocker->blockResults());
    }
}
