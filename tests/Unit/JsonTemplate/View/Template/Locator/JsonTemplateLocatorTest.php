<?php

declare(strict_types=1);

namespace Tests\Unit\JsonTemplate\View\Template\Locator;

use App\JsonTemplate\Settings\JsonTemplateSettings;
use App\JsonTemplate\Template\Locator\JsonTemplateLocation;
use App\JsonTemplate\Template\Locator\JsonTemplateLocator;
use App\SplitTest\SplitTestExtendedReaderInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\Stub\JsonTemplate\Request\JsonTemplateRequestStub;
use Twig\Environment;
use Twig\Loader\LoaderInterface;
use Twig\Source;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

class JsonTemplateLocatorTest extends PhpUnitTestCase
{
    private JsonTemplateRequestStub $jsonTemplateRequestStub;

    private LoaderInterface & MockObject $loaderMock;

    private Environment & MockObject $environmentMock;

    protected function setUp(): void
    {
        $this->jsonTemplateRequestStub = new JsonTemplateRequestStub();

        $this->loaderMock = $this->createMock(LoaderInterface::class);
        $this->environmentMock = $this->createConfiguredMock(
            Environment::class,
            [
                'getLoader' => $this->loaderMock,
            ],
        );
    }

    /**
     * @return mixed[]
     */
    public static function locateDataProvider(): array
    {
        return [
            'default template'                                                   => [
                'configuredVariant'       => null,
                'configuredVariantExists' => null,
                'variantParameter'        => null,
                'variantExists'           => null,
                'expectedVariant'         => null,
                'expectedTemplate'        => '@themeJson/test/template.json',
            ],
            'with variant parameter not existing'                                => [
                'configuredVariant'       => null,
                'configuredVariantExists' => false,
                'variantParameter'        => 'henkie',
                'variantExists'           => false,
                'expectedVariant'         => null,
                'expectedTemplate'        => '@themeJson/test/template.json',
            ],
            'with variant parameter existing'                                    => [
                'configuredVariant'       => null,
                'configuredVariantExists' => false,
                'variantParameter'        => 'pizza',
                'variantExists'           => true,
                'expectedVariant'         => 'pizza',
                'expectedTemplate'        => '@themeJson/test/template-pizza.json',
            ],
            'with variant parameter existing, configured parameter not existing' => [
                'configuredVariant'       => 'henkie',
                'configuredVariantExists' => false,
                'variantParameter'        => 'pizza',
                'variantExists'           => true,
                'expectedVariant'         => 'pizza',
                'expectedTemplate'        => '@themeJson/test/template-pizza.json',
            ],
            'with variant parameter not existing, configured parameter existing' => [
                'configuredVariant'       => 'henkie',
                'configuredVariantExists' => true,
                'variantParameter'        => 'pizza',
                'variantExists'           => false,
                'expectedVariant'         => null,
                'expectedTemplate'        => '@themeJson/test/template.json',
            ],
            'with variant parameter existing, configured parameter existing'     => [
                'configuredVariant'       => 'henkie',
                'configuredVariantExists' => true,
                'variantParameter'        => 'pizza',
                'variantExists'           => true,
                'expectedVariant'         => 'pizza',
                'expectedTemplate'        => '@themeJson/test/template-pizza.json',
            ],
        ];
    }

    #[DataProvider('locateDataProvider')]
    public function testLocate(
        ?string $configuredVariant,
        ?bool $configuredVariantExists,
        ?string $variantParameter,
        ?bool $variantExists,
        ?string $expectedVariant,
        string $expectedTemplate
    ): void
    {
        $this->jsonTemplateRequestStub
            ->setTemplateVariant($variantParameter);

        $loaderReturns = [];

        if ($variantParameter !== null) {
            $loaderReturns[] = $variantExists;
        }

        if ($configuredVariant !== null) {
            $loaderReturns[] = $configuredVariantExists;
        }

        $this->loaderMock
            ->method('exists')
            ->willReturnOnConsecutiveCalls(...$loaderReturns);

        $this->loaderMock
            ->method('getSourceContext')
            ->willReturn(
                new Source('code', 'name', 'twig_template_path'),
            );

        $locator = new JsonTemplateLocator(
            $this->jsonTemplateRequestStub,
            new JsonTemplateSettings(
                templateVariant  : $configuredVariant,
                templateOverrides: null,
            ),
            $this->environmentMock,
            $this->createMock(SplitTestExtendedReaderInterface::class),
        );
        $location = $locator->locate('@themeJson/test/template.json');

        self::assertEquals(
            new JsonTemplateLocation(
                $expectedTemplate,
                'twig_template_path',
                $expectedVariant,
            ),
            $location,
        );
    }
}
