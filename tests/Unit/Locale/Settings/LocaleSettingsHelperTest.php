<?php

declare(strict_types=1);

namespace Tests\Unit\Locale\Settings;

use App\Domain\Settings\DomainSettings;
use App\Domain\Settings\DomainSettingsHelperInterface;
use App\Locale\Model\LocaleFactory;
use App\Locale\Request\LocaleRequestInterface;
use App\Locale\Settings\LocaleConfigDefaultNotFoundException;
use App\Locale\Settings\LocaleSettings;
use App\Locale\Settings\LocaleSettingsFactory;
use App\Locale\Settings\LocaleSettingsHelper;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class LocaleSettingsHelperTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function localesConfigDataProvider(): array
    {
        $localeFactory = new LocaleFactory();
        $localeEnGb = $localeFactory->create('en_GB');
        $localeNlNl = $localeFactory->create('nl_NL');

        return [
            'single default locale'                      => [
                'localesConfig'             => [
                    [
                        'locale'     => 'nl_NL',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => null,
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::DIRECT,
                'expectedLocaleSettings'    => new LocaleSettings($localeNlNl, true),
                'expectException'           => null,
            ],
            'multiple locales default locale'            => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => false,
                    ],
                    [
                        'locale'     => 'nl_NL',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => null,
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::DIRECT,
                'expectedLocaleSettings'    => new LocaleSettings($localeNlNl, true),
                'expectException'           => null,
            ],
            'multiple locales select locale'             => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => false,
                    ],
                    [
                        'locale'     => 'nl_NL',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => 'en_GB',
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::DIRECT,
                'expectedLocaleSettings'    => new LocaleSettings($localeEnGb, false),
                'expectException'           => null,
            ],
            'multiple locales select unknown'            => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => false,
                    ],
                    [
                        'locale'     => 'nl_NL',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => 'no_NO',
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::DIRECT,
                'expectedLocaleSettings'    => new LocaleSettings($localeNlNl, true),
                'expectException'           => null,
            ],
            'no default locale'                          => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => false,
                    ],
                    [
                        'locale'     => 'nl_NL',
                        'is_default' => false,
                    ],
                ],
                'locale'                    => null,
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::DIRECT,
                'expectedLocaleSettings'    => null,
                'expectException'           => LocaleConfigDefaultNotFoundException::class,
            ],
            'google adsense online, no locale'           => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => null,
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::ONLINE,
                'expectedLocaleSettings'    => new LocaleSettings($localeEnGb, true),
                'expectException'           => null,
            ],
            'google adsense online, no supported locale' => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => 'ar_SA',
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::ONLINE,
                'expectedLocaleSettings'    => new LocaleSettings($localeEnGb, true),
                'expectException'           => null,
            ],
            'google adsense online, supported locale'    => [
                'localesConfig'             => [
                    [
                        'locale'     => 'en_GB',
                        'is_default' => true,
                    ],
                ],
                'locale'                    => 'nl_NL',
                'googleAdSenseEnabled'      => true,
                'googleAdSenseContractType' => ContractType::ONLINE,
                'expectedLocaleSettings'    => new LocaleSettings($localeNlNl, false),
                'expectException'           => null,
            ],
        ];
    }

    /**
     * @param mixed[] $localesConfig
     */
    #[DataProvider('localesConfigDataProvider')]
    public function testCreateLocaleSettings(
        array $localesConfig,
        ?string $locale,
        bool $googleAdSenseEnabled,
        ContractType $googleAdSenseContractType,
        ?LocaleSettings $expectedLocaleSettings,
        ?string $expectException
    ): void
    {
        if ($expectException !== null) {
            $this->expectException($expectException);
        }

        $localeSettingsFactory = new LocaleSettingsFactory(
            new LocaleFactory(),
        );

        $brandConfig = [
            GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE => [
                'contract_type' => $googleAdSenseContractType->value,
            ],
        ];

        $domainConfig = [
            DomainSettings::KEY_HOST                         => 'host.com',
            LocaleSettingsHelper::KEY_LOCALES                => $localesConfig,
            GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE => [
                'enabled' => $googleAdSenseEnabled,
            ],
        ];

        $localeSettingsHelper = new LocaleSettingsHelper(
            $this->createMock(WebsiteConfigurationHelper::class),
            $this->createMock(LocaleSettingsFactory::class),
            $this->createMock(LocaleRequestInterface::class),
            $this->createMock(DomainSettingsHelperInterface::class),
        );
        $localeConfig = $localeSettingsHelper->getLocaleConfig($brandConfig, $domainConfig, $locale);

        if ($expectedLocaleSettings !== null) {
            $localeSettings = $localeSettingsFactory->create($localeConfig);

            self::assertSame($expectedLocaleSettings->locale->code, $localeSettings->locale->code);
            self::assertSame($expectedLocaleSettings->isDefault, $localeSettings->isDefault);
        }
    }
}
