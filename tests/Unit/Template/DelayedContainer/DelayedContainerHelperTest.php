<?php

declare(strict_types=1);

namespace Tests\Unit\Template\DelayedContainer;

use App\BingAds\Helper\BingAdsHelper;
use App\Generic\Device\Device;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\Template\DelayedContainer\DelayedContainerHelper;
use App\Tracking\Helper\TrafficHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\GoogleCsa\GoogleCsaStubBuilder;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Visymo\GoogleCsa\Ads\Unit\AdUnit;

class DelayedContainerHelperTest extends TestCase
{
    private GoogleCsaRegistry $googleCsaRegistry;

    private MockObject & BingAdsHelper $bingAdsHelperMock;

    private MockObject & TrafficHelper $trafficHelperMock;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    private RequestInfoStub $requestInfoStub;

    private DelayedContainerHelper $delayedContainerHelper;

    protected function setUp(): void
    {
        $this->googleCsaRegistry = new GoogleCsaRegistry();
        $this->bingAdsHelperMock = $this->createMock(BingAdsHelper::class);
        $this->trafficHelperMock = $this->createMock(TrafficHelper::class);
        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $this->requestInfoStub = new RequestInfoStub();

        $this->delayedContainerHelper = new DelayedContainerHelper(
            $this->googleCsaRegistry,
            $this->bingAdsHelperMock,
            $this->trafficHelperMock,
            $this->activeTrackingEntryHelperStub,
            $this->requestInfoStub,
        );
    }

    /**
     * @return mixed[]
     */
    public static function isDelayedContainerRequiredDataProvider(): array
    {
        return [
            'not required'           => [
                'hasGoogleUnits'     => false,
                'hasBingUnits'       => false,
                'isPaidTraffic'      => true,
                'expectedIsRequired' => false,
            ],
            'required, google units' => [
                'hasGoogleUnits'     => true,
                'hasBingUnits'       => false,
                'isPaidTraffic'      => true,
                'expectedIsRequired' => true,
            ],
            'required, bing ads'     => [
                'hasGoogleUnits'     => false,
                'hasBingUnits'       => true,
                'isPaidTraffic'      => true,
                'expectedIsRequired' => true,
            ],
            'required'               => [
                'hasGoogleUnits'     => true,
                'hasBingUnits'       => true,
                'isPaidTraffic'      => true,
                'expectedIsRequired' => true,
            ],
            'not paid traffic'       => [
                'hasGoogleUnits'     => false,
                'hasBingUnits'       => false,
                'isPaidTraffic'      => false,
                'expectedIsRequired' => false,
            ],
        ];
    }

    #[DataProvider('isDelayedContainerRequiredDataProvider')]
    public function testIsDelayedContainerRequired(
        bool $hasGoogleUnits,
        bool $hasBingUnits,
        bool $isPaidTraffic,
        bool $expectedIsRequired
    ): void
    {
        if ($hasGoogleUnits) {
            $googleCsaStubBuilder = new GoogleCsaStubBuilder();
            $googleCsa = $googleCsaStubBuilder
                ->withPublisherId('abc')
                ->withQuery('pizza')
                ->create();
            $googleCsa->ads()->addUnit(
                new AdUnit('csa-top', null, 5, true),
            );
            $this->googleCsaRegistry->setGoogleCsa($googleCsa);
        } else {
            $this->googleCsaRegistry->setGoogleCsa(null);
        }

        $this->bingAdsHelperMock->method('hasUnits')->willReturn($hasBingUnits);
        $this->trafficHelperMock->method('isPaidTraffic')->willReturn($isPaidTraffic);

        self::assertSame($expectedIsRequired, $this->delayedContainerHelper->isDelayedContainerRequired());
    }

    /**
     * @return mixed[]
     */
    public static function showDelayedContainerLoaderDataProvider(): array
    {
        return [
            'no: desktop'          => [
                'isPaidTraffic'                      => true,
                'route'                              => 'route_display_search_related',
                'device'                             => Device::DESKTOP,
                'expectedShowDelayedContainerLoader' => false,
            ],
            'no: not paid traffic' => [
                'isPaidTraffic'                      => false,
                'route'                              => 'route_display_search_related',
                'device'                             => Device::MOBILE,
                'expectedShowDelayedContainerLoader' => false,
            ],
            'no: wrong route'      => [
                'isPaidTraffic'                      => true,
                'route'                              => 'route_web_search',
                'device'                             => Device::MOBILE,
                'expectedShowDelayedContainerLoader' => false,
            ],
            'yes: dsr'             => [
                'isPaidTraffic'                      => true,
                'route'                              => 'route_display_search_related',
                'device'                             => Device::MOBILE,
                'expectedShowDelayedContainerLoader' => true,
            ],
            'yes: dsrw'            => [
                'isPaidTraffic'                      => true,
                'route'                              => 'route_display_search_related_web',
                'device'                             => Device::MOBILE,
                'expectedShowDelayedContainerLoader' => true,
            ],
        ];
    }

    #[DataProvider('showDelayedContainerLoaderDataProvider')]
    public function testShowDelayedContainerLoader(
        bool $isPaidTraffic,
        string $route,
        Device $device,
        bool $expectedShowDelayedContainerLoader
    ): void
    {
        $this->trafficHelperMock->method('isPaidTraffic')->willReturn($isPaidTraffic);
        $this->bingAdsHelperMock->method('hasUnits')->willReturn(true);
        $this->requestInfoStub->setRoute($route);

        $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->clear()
            ->setQuery('pizza')
            ->setDevice($device)
            ->create();

        self::assertSame(
            $expectedShowDelayedContainerLoader,
            $this->delayedContainerHelper->showDelayedContainerLoader(),
        );
    }
}
