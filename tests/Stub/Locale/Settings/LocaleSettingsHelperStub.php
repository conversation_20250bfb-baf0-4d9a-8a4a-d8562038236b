<?php

declare(strict_types=1);

namespace Tests\Stub\Locale\Settings;

use App\Locale\Model\LocaleFactory;
use App\Locale\Settings\LocaleSettings;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class LocaleSettingsHelperStub implements LocaleSettingsHelperInterface
{
    private const array TEST_CODES = [
        'en_US',
        'de_DE',
        'nl_NL',
        'fr_FR',
    ];

    private LocaleSettings $localeSettings;

    public function __construct(
        private readonly LocaleFactory $localeFactory = new LocaleFactory()
    )
    {
        $this->setLocale(
            TestStubRandomizer::pickItem(self::TEST_CODES),
            TestStubRandomizer::pickBoolean(),
        );
    }

    public function getSettings(): LocaleSettings
    {
        return $this->localeSettings;
    }

    public function setLocale(string $locale, bool $isDefault = true): self
    {
        $this->localeSettings = new LocaleSettings(
            locale   : $this->localeFactory->create($locale),
            isDefault: $isDefault,
        );

        return $this;
    }

    /** @inheritDoc */
    public function getLocaleConfig(array $brandConfig, array $domainConfig, ?string $locale = null): array
    {
        return [
            LocaleSettings::KEY_LOCALE     => $locale ?? 'en_US',
            LocaleSettings::KEY_IS_DEFAULT => $locale === null,
        ];
    }
}
