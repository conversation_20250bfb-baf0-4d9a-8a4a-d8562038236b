<?php

declare(strict_types=1);

namespace App\GoogleCsa\Generator;

use App\Debug\Request\DebugRequestInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;

final class GoogleRelatedTermsContainerGenerator implements GoogleRelatedTermsContainerGeneratorInterface
{
    private static int $containerCount = 0;

    /** @var array<string, string> */
    private array $componentContainerMapping = [];

    public function __construct(
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly DebugRequestInterface $debugRequest
    )
    {
    }

    public function generateContainerForComponent(
        ComponentInterface $component,
        ?string $suffix
    ): string
    {
        if (isset($this->componentContainerMapping[$component->getId()])) {
            return $this->componentContainerMapping[$component->getId()];
        }

        self::$containerCount++;
        $containerWithSuffix = $this->getContainerWithSuffix($suffix);
        $containerPrefix = $this->getContainerPrefix();

        $this->componentContainerMapping[$component->getId()] = $containerPrefix !== null
            ? sprintf('%s-%s', $containerPrefix, $containerWithSuffix)
            : $containerWithSuffix;

        return $this->componentContainerMapping[$component->getId()];
    }

    private function getContainerWithSuffix(?string $suffix = null): string
    {
        $splitTestContainerSuffix = $this->splitTestExtendedReader->getContainerSuffix();
        $debugSuffix = $this->debugRequest->getDebugForceCsaContainerSuffix();
        $additionalContainerSuffix = $debugSuffix ?? $splitTestContainerSuffix;

        if ($suffix === null && $this->splitTestExtendedReader->isVariantActive('krdd')) {
            $suffix = 'krdd';
        }

        $container = $suffix !== null
            ? sprintf(self::CONTAINER_SUFFIXED_TEMPLATE, $suffix, self::$containerCount)
            : sprintf(self::CONTAINER_TEMPLATE, self::$containerCount);

        if ($additionalContainerSuffix !== null) {
            $container = sprintf('%s-%s', $container, $additionalContainerSuffix);
        }

        return $container;
    }

    private function getContainerPrefix(): ?string
    {
        return $this->debugRequest->getDebugForceCsaContainerPrefix();
    }
}
