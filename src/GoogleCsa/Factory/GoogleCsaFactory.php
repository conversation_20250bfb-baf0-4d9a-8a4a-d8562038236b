<?php

declare(strict_types=1);

namespace App\GoogleCsa\Factory;

use App\Ads\AdTestHelper;
use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseConversionUrlGeneratorInterface;
use App\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsConversionUrlGeneratorInterface;
use App\GoogleCsa\Helper\GoogleCsaClickTrackUrlHelper;
use App\GoogleCsa\Parameter\GoogleCsaIgnorePageParameterInterface;
use App\GoogleCsa\Parameter\GoogleCsaPublisherIdParameterInterface;
use App\GoogleCsa\Parameter\GoogleCsaRelatedTermsResultsPageBaseUrlParameterInterface;
use App\GoogleCsa\Request\GoogleCsaRequest;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameterInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Search\Request\SearchRequestInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Visymo\GoogleCsa\Ads\Unit\AdUnitFactory;
use Visymo\GoogleCsa\Enum\LinkTarget;
use Visymo\GoogleCsa\GoogleCsa;
use Visymo\GoogleCsa\GoogleCsaInterface;
use Visymo\GoogleCsa\Property\FunctionProperty;
use Visymo\GoogleCsa\RelatedSearch\RelatedSearchTargeting;
use Visymo\GoogleCsa\Unit\UnitInterface;

readonly class GoogleCsaFactory
{
    private const string CALLBACK_ADS_RESPONSE        = 'gAdsResponse';
    private const string CALLBACK_ADS_SCRIPT_LOADED   = 'gAdsScriptLoaded';
    private const string CALLBACK_AD_UNIT_LOADED      = 'gAdUnitLoaded';
    private const string CALLBACK_AD_UNIT_RENDER      = 'gAdUnitRender';
    private const string CALLBACK_RELATED_RESPONSE    = 'gRelatedResponse';
    private const string CALLBACK_RELATED_UNIT_LOADED = 'gRelatedUnitLoaded';
    private const string CALLBACK_RELATED_UNIT_RENDER = 'gRelatedUnitRender';

    public function __construct(
        private LocaleSettingsHelperInterface $localeSettingsHelper,
        private SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private GoogleCsaPublisherIdParameterInterface $googleCsaPublisherIdParameter,
        private GoogleCsaStyleIdParameterInterface $googleCsaStyleIdParameter,
        private GoogleCsaRelatedTermsResultsPageBaseUrlParameterInterface $googleCsaRelatedTermsResultsPageBaseUrlParameter,
        private GoogleCsaIgnorePageParameterInterface $googleCsaIgnorePageParameter,
        private GoogleAdSenseConversionUrlGeneratorInterface $googleAdSenseConversionUrlGenerator,
        private GoogleRelatedTermsConversionUrlGeneratorInterface $googleRelatedTermsConversionUrlGenerator,
        private AdUnitFactory $adUnitFactory,
        private AdTestHelper $adTestHelper,
        private GoogleCsaClickTrackUrlHelper $googleCsaClickTrackUrlHelper
    )
    {
    }

    public function create(GoogleCsaRequest $googleCsaRequest): ?GoogleCsa
    {
        if (!$this->shouldCreate($googleCsaRequest)) {
            return null;
        }

        $googleCsa = new GoogleCsa(
            publisherId: $this->googleCsaPublisherIdParameter->getPublisherId(),
            query      : $this->getQuery($googleCsaRequest),
            adTest     : $this->adTestHelper->isGoogleAdSenseTest(),
        );

        $googleCsa
            ->setJsLoadedCallback(self::CALLBACK_ADS_SCRIPT_LOADED)
            ->setTargetLanguage($this->localeSettingsHelper->getSettings()->locale->language->code)
            ->setLinkTarget(LinkTarget::BLANK)
            ->setAdSafe($googleCsaRequest->adSafe)
            ->addChannels($googleCsaRequest->channels);

        if (!$this->splitTestExtendedReader->isVariantActive('encwi')) {
            $googleCsa->setIvt(false);
        }

        $this
            ->setAdSettings($googleCsa, $googleCsaRequest)
            ->setRelatedSearchSettings($googleCsa, $googleCsaRequest);

        return $googleCsa;
    }

    private function shouldCreate(GoogleCsaRequest $googleCsaRequest): bool
    {
        if ($googleCsaRequest->ads === null && $googleCsaRequest->relatedSearch === null) {
            return false;
        }

        // Query required for ads
        if ($googleCsaRequest->ads !== null && $googleCsaRequest->query === null) {
            return false;
        }

        // Query required for related search if not for content
        if ($googleCsaRequest->relatedSearch?->forContent === false && $googleCsaRequest->query === null) {
            return false;
        }

        return true;
    }

    private function getQuery(GoogleCsaRequest $googleCsaRequest): ?string
    {
        // Query required for ads
        if ($googleCsaRequest->ads !== null) {
            return $googleCsaRequest->query;
        }

        // No query needed for related search for content
        if ($googleCsaRequest->relatedSearch?->forContent === true) {
            return null;
        }

        return $googleCsaRequest->query;
    }

    private function setAdSettings(GoogleCsaInterface $googleCsa, GoogleCsaRequest $googleCsaRequest): self
    {
        $adsRequest = $googleCsaRequest->ads;

        if ($adsRequest === null) {
            return $this;
        }

        // Apply style ID on ad unit level. This will style the ad units, but not the related terms units (as intended).
        // A different style ID on ad units will not do anything.
        // Google applies the style ID of the first ad unit to all ad units.
        $styleId = $this->googleCsaStyleIdParameter->getStyleId();
        $adUnits = [];

        if ($adsRequest->topAmount > 0) {
            $adUnits[] = $this->adUnitFactory
                ->create(
                    container: (string)$adsRequest->topContainer,
                    styleId  : $styleId,
                    number   : $adsRequest->topAmount,
                    isTopAd  : true,
                )
                ->setAdLoadedCallback(self::CALLBACK_AD_UNIT_LOADED)
                ->setAdsResponseCallback(self::CALLBACK_ADS_RESPONSE)
                ->setIframeHeightCallback(self::CALLBACK_AD_UNIT_RENDER);
        }

        if ($adsRequest->bottomAmount > 0) {
            $adUnits[] = $this->adUnitFactory
                ->create(
                    container: (string)$adsRequest->bottomContainer,
                    styleId  : $styleId,
                    number   : $adsRequest->bottomAmount,
                    isTopAd  : false,
                )
                ->setAdLoadedCallback(self::CALLBACK_AD_UNIT_LOADED);
        }

        $googleCsa
            ->ads()
            ->setNumRepeated($adsRequest->numRepeated)
            ->setAdPage($adsRequest->page);

        $clickTrackUrl = $adsRequest->addClickTrackUrl
            ? $this->googleAdSenseConversionUrlGenerator->generate(adClientId: $googleCsa->getPublisherId())
            : null;

        foreach ($adUnits as $adUnit) {
            $googleCsa->ads()->addUnit($adUnit);

            if ($clickTrackUrl === null) {
                continue;
            }

            $clickTrackUrlEncoded = json_encode(
                $this->googleCsaClickTrackUrlHelper->updateClickTrackUrl(
                    $clickTrackUrl,
                    $adUnit->getStyleId(),
                ),
                JSON_UNESCAPED_SLASHES | JSON_THROW_ON_ERROR,
            );

            // SERP-3717
            // Overwrite 'clicktrackUrl' property
            // Call JavaScript function 'gCompleteUrl' to add additional URL parameters
            $adUnit->getCustomProperties()->setProperty(
                FunctionProperty::create(
                    UnitInterface::PROPERTY_CLICK_TRACK_URL,
                    sprintf('gCompleteUrl(%s)', $clickTrackUrlEncoded),
                ),
                false,
            );
        }

        return $this;
    }

    private function setRelatedSearchSettings(GoogleCsaInterface $googleCsa, GoogleCsaRequest $googleCsaRequest): void
    {
        $relatedSearchRequest = $googleCsaRequest->relatedSearch;

        if ($relatedSearchRequest === null) {
            return;
        }

        $setResponseCallback = true;
        $clickTrackUrl = $this->googleRelatedTermsConversionUrlGenerator->generate(
            adClientId: $googleCsa->getPublisherId(),
        );

        foreach ($relatedSearchRequest->units as $relatedSearchUnit) {
            $relatedSearchUnit
                ->setAdLoadedCallback(self::CALLBACK_RELATED_UNIT_LOADED)
                ->setIframeHeightCallback(self::CALLBACK_RELATED_UNIT_RENDER);

            if ($setResponseCallback) {
                $relatedSearchUnit->setAdsResponseCallback(self::CALLBACK_RELATED_RESPONSE);
                $setResponseCallback = false;
            }

            $relatedSearchUnit->addClickTrackUrl(
                $this->googleCsaClickTrackUrlHelper->updateClickTrackUrl(
                    $clickTrackUrl,
                    $relatedSearchUnit->getStyleId(),
                ),
            );

            $googleCsa->relatedSearch()->addUnit($relatedSearchUnit);
        }

        $googleCsa->relatedSearch()
            ->setResultsPageQueryParam(SearchRequestInterface::PARAMETER_QUERY)
            ->setResultsPageBaseUrl(
                $this->googleCsaRelatedTermsResultsPageBaseUrlParameter->getResultsPageBaseUrl(
                    $relatedSearchRequest->route,
                ),
            )
            ->setTerms($relatedSearchRequest->terms);

        if (!$relatedSearchRequest->forContent) {
            return;
        }

        $googleCsa->relatedSearch()
            ->setRelatedSearchTargeting(RelatedSearchTargeting::CONTENT)
            ->setReferrerAdCreative($relatedSearchRequest->referrerAdCreative)
            ->addIgnoredPageParams(
                $this->googleCsaIgnorePageParameter->getIgnoreParams(),
            );
    }
}
