<?php

declare(strict_types=1);

namespace App\BingAds\Factory;

use App\Ads\AdTestHelper;
use App\BingAdsStyle\BingAdsStyle;
use App\BingAdsStyle\BingAdsStyleHelper;
use App\BingAdsStyle\BingAdsStyleRepository;
use App\ConversionTracking\Endpoint\BingAds\BingAdsConversionUrlGenerator;
use App\ConversionTracking\Endpoint\BingAds\BingAdsRequestInterface;
use App\Debug\Request\DebugRequestInterface;
use App\GeoIp\GeoIp2CountryHelper;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Monetization\Settings\MonetizationSettings;
use App\Preferences\Helper\PreferencesHelper;
use App\Search\Request\SearchRequestInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Tracking\Helper\TrafficHelper;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use App\WebsiteSettings\Settings\BingAds\BingAdsSettings;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Psr\Log\LoggerInterface;
use Visymo\BingAds\AdUnit\AdUnit;
use Visymo\BingAds\BingAds;
use Visymo\BingAds\Clarity\Clarity;
use Visymo\BingAds\Clarity\Exception\ClarityAlreadyRenderedException;
use Visymo\BingAds\Clarity\Factory\ClarityFactory;
use Visymo\BingAds\Exception\BingAdsTracingTagException;
use Visymo\BingAds\PageOptions\Enum\LegalTextOption;
use Visymo\BingAds\PageOptions\Enum\SafeSearch;
use Visymo\BingAds\PageOptions\PageOptions;

class BingAdsFactory
{
    public const string ADS_RENDERED_CALLBACK = 'bAdsUnitLoaded';

    public function __construct(
        private readonly SearchRequestInterface $searchRequest,
        private readonly DebugRequestInterface $debugRequest,
        private readonly WebsiteSettingsHelper $websiteSettingsHelper,
        private readonly PreferencesHelper $preferencesHelper,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly GeoIp2CountryHelper $geoIpCountryHelper,
        private readonly TrafficHelper $trafficHelper,
        private readonly BingAdsQueryParameter $bingAdsQueryParameter,
        private readonly BingAdsStyleRepository $bingAdsStyleRepository,
        private readonly BingAdsStyleHelper $bingAdsStyleHelper,
        private readonly BingAdsStyleIdParameter $bingAdsStyleIdParameter,
        private readonly BingAdsConversionUrlGenerator $bingAdsConversionUrlGenerator,
        private readonly AdTestHelper $adTestHelper,
        private readonly ClarityFactory $clarityFactory,
        private readonly LoggerInterface $logger,
        private readonly TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker,
        private readonly LocaleSettingsHelperInterface $localeSettingsHelper,
        private readonly MonetizationSettings $monetizationSettings
    )
    {
    }

    /**
     * @param AdUnit[] $adUnits
     */
    public function create(array $adUnits, ?int $adStyleId): ?BingAds
    {
        if (!$this->monetizationSettings->adsEnabled) {
            return null;
        }

        if ($adUnits === []) {
            return null;
        }

        $query = $this->bingAdsQueryParameter->getQuery();

        if ($query === null || $this->debugRequest->disableBingAds()) {
            return null;
        }

        if ($this->trademarkInfringementResultBlocker->blockResults()) {
            return null;
        }

        $bingAdsSettings = $this->websiteSettingsHelper->getSettings()->getBingAds();

        if (!$bingAdsSettings->isEnabled()) {
            return null;
        }

        $publisherId = $this->getPublisherId($bingAdsSettings);

        // No personalized Ads for EU visitors, otherwise Bing Ads default (null)
        $personalizedAds = $this->geoIpCountryHelper->isEuVisitor() === true ? false : null;
        $trackingPixelUrl = $this->bingAdsConversionUrlGenerator->generate($publisherId);
        $trackingPixelUrlWithBlockTemplate = sprintf(
            '%s%s%s=',
            $trackingPixelUrl,
            str_contains($trackingPixelUrl, '?') ? '&' : '?',
            BingAdsRequestInterface::PARAMETER_BLOCK,
        );

        $bingAdsStyle = $this->getAdStyle($adStyleId);
        $bingAds = new BingAds(
            adUnitId: $publisherId,
            query   : $query,
            testMode: $this->adTestHelper->isBingAdsTest(),
            clarity : $this->getClarity($bingAdsSettings),
        );

        // Page options
        $pageOptions = $bingAds->getPageOptions();
        $pageOptions->pageNumber = $this->searchRequest->getPage();
        $pageOptions->adLanguage = $this->localeSettingsHelper->getSettings()->locale->language->code;
        $pageOptions->personalization = $personalizedAds;
        $pageOptions->boldKeyWords = false;
        $pageOptions->linkTargetNewTab = true;
        $pageOptions->clickOnAllText = false;
        $pageOptions->enableCookie = false;
        $pageOptions->legalTextOption = LegalTextOption::DISPLAY_URL_ICON;
        $pageOptions->safeSearch = $this->preferencesHelper->getSafeSearch() ?? false
            ? SafeSearch::STRICT
            : SafeSearch::MODERATE;
        $pageOptions->adsRenderedCallback = self::ADS_RENDERED_CALLBACK;

        $this->setTracingTags($pageOptions);
        $this->bingAdsStyleHelper->applyStyleForPageOptions($pageOptions, $bingAdsStyle);

        // Ad units
        $block = 1;

        foreach ($adUnits as $adUnit) {
            $this->bingAdsStyleHelper->applyStyleForAdUnit($adUnit, $bingAdsStyle);
            $this->setAdUnitClickTrackUrl($adUnit, $block, $trackingPixelUrlWithBlockTemplate);

            $pageOptions->addAdUnit($adUnit);
            $block++;
        }

        return $bingAds;
    }

    private function getPublisherId(BingAdsSettings $bingAdsSettings): string
    {
        return $this->searchRequest->isLandingPage()
            ? $bingAdsSettings->getSemAdUnitId()
            : $bingAdsSettings->getWebAdUnitId();
    }

    private function setAdUnitClickTrackUrl(
        AdUnit $adUnit,
        int $block,
        string $trackingPixelUrlWithBlockTemplate
    ): void
    {
        $adUnit->clickTrackUrl = sprintf('%s%u', $trackingPixelUrlWithBlockTemplate, $block);
    }

    private function setTracingTags(PageOptions $pageOptions): void
    {
        $this->addTracingTag($pageOptions, $this->trafficHelper->getTrackingChannel());
        $this->addTracingTag($pageOptions, $this->splitTestExtendedReader->getChannel());
    }

    private function addTracingTag(PageOptions $pageOptions, ?string $tracingTag): void
    {
        if ($tracingTag === null) {
            return;
        }

        try {
            $pageOptions->addTracingTag($tracingTag);
        } catch (BingAdsTracingTagException $exception) {
            // Log exception, but continue to show ads
            $this->logger->error(
                sprintf('Caught "%s" while adding Bing Ads tracing tags', BingAdsTracingTagException::class),
                [
                    'exception'   => $exception,
                    'message'     => $exception->getMessage(),
                    'tracing_tag' => $tracingTag,
                ],
            );
        }
    }

    private function getAdStyle(?int $adStyleId): ?BingAdsStyle
    {
        if ($this->debugRequest->forceStyleId() !== null) {
            return $this->bingAdsStyleRepository->findById($this->debugRequest->forceStyleId(), false);
        }

        return $this->bingAdsStyleRepository->findById(
            $adStyleId ?? $this->bingAdsStyleIdParameter->getStyleId(),
            true,
        );
    }

    private function getClarity(BingAdsSettings $bingAdsSettings): ?Clarity
    {
        try {
            return $bingAdsSettings->getClarityId() !== null
                ? $this->clarityFactory->create($bingAdsSettings->getClarityId())
                : null;
        } catch (ClarityAlreadyRenderedException $exception) {
            $this->logger->error($exception->getMessage());

            return null;
        }
    }
}
