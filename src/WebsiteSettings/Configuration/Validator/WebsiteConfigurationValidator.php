<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\Validator;

use App\Campaign\Settings\CampaignSettingsFactory;
use App\Locale\Settings\LocaleSettingsFactory;
use App\Locale\Settings\LocaleSettingsHelper;
use App\WebsiteSettings\Configuration\Validator\Exception\WebsiteConfigValidationFailedException;
use App\WebsiteSettings\Configuration\Validator\Exception\WebsiteJsonConfigValidationFailedException;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFactory;
use App\WebsiteSettings\Settings\WebsiteSettingsFactory;
use Visymo\Shared\Domain\Validator\JsonSchemaValidator;

final readonly class WebsiteConfigurationValidator
{
    public function __construct(
        private JsonSchemaValidator $websiteConfigurationJsonSchemaValidator,
        private WebsiteConfigurationFactory $websiteConfigurationFactory,
        private LocaleSettingsFactory $localeSettingsFactory,
        private WebsiteSettingsFactory $websiteSettingsFactory,
        private CampaignSettingsFactory $campaignSettingsFactory
    )
    {
    }

    /**
     * @param mixed[] $jsonConfig
     */
    public function validateJsonConfig(array $jsonConfig): void
    {
        try {
            $this->websiteConfigurationJsonSchemaValidator->assert($jsonConfig);
        } catch (\Throwable $exception) {
            throw WebsiteJsonConfigValidationFailedException::create($exception);
        }
    }

    /**
     * @param mixed[] $config
     */
    public function validateConfig(array $config): void
    {
        try {
            $websiteConfiguration = $this->websiteConfigurationFactory->createFromNormalized($config);

            $this->validateDomainsLocales($websiteConfiguration);
        } catch (\Throwable $exception) {
            throw WebsiteConfigValidationFailedException::create($exception);
        }
    }

    private function validateDomainsLocales(WebsiteConfiguration $websiteConfiguration): void
    {
        $domainsConfig = $websiteConfiguration->getDomainsConfig();
        $domains = array_keys($domainsConfig);

        // Pick random domains to speed up the validation.
        if (count($domainsConfig) > 3) {
            $domains = array_rand($domainsConfig, 3);
        }

        foreach ($websiteConfiguration->getDomainsConfig() as $domain => $domainConfig) {
            $localeConfigs = $domainConfig[LocaleSettingsHelper::KEY_LOCALES];
            $defaults = 0;
            $uniqueLocales = [];

            if (in_array($domain, $domains, true)) {
                $this->validateDomainSettings(
                    websiteConfiguration: $websiteConfiguration,
                    domain              : $domain,
                    accountIds          : $websiteConfiguration->getAccountIds(),
                );
            }

            foreach ($localeConfigs as $localeConfig) {
                $localeSettings = $this->localeSettingsFactory->create($localeConfig);

                if ($localeSettings->isDefault) {
                    $defaults++;
                }

                $locale = $localeSettings->locale->code;

                // Locale must be unique
                if (isset($uniqueLocales[$locale])) {
                    throw new \RuntimeException(
                        sprintf(
                            'Domain "%s" locale settings locale %s already exists.',
                            $domain,
                            $locale,
                        ),
                    );
                }

                $uniqueLocales[$locale] = true;
            }

            // Exactly one default
            if ($defaults !== 1) {
                throw new \RuntimeException(sprintf('Domain "%s" locale settings has %d defaults.', $domain, $defaults));
            }
        }
    }

    /**
     * @param int[] $accountIds
     */
    private function validateDomainSettings(
        WebsiteConfiguration $websiteConfiguration,
        string $domain,
        array $accountIds
    ): void
    {
        foreach ($accountIds as $accountId) {
            $this->validateCampaignSettings($websiteConfiguration, $accountId);

            // Test that the configuration can be successfully built for domain, locale and account
            $websiteSettings = $this->websiteSettingsFactory->create(
                websiteConfiguration: $websiteConfiguration,
                domain              : $domain,
                accountId           : $accountId,
            );

            // To array will check all internal models and touch most properties
            $websiteSettings->toArray();
        }

        // Test that the configuration can be successfully built for domain and locale without account
        $websiteSettings = $this->websiteSettingsFactory->create(
            websiteConfiguration: $websiteConfiguration,
            domain              : $domain,
            accountId           : null,
        );

        // To array will check all internal models and touch most properties
        $websiteSettings->toArray();
    }

    private function validateCampaignSettings(
        WebsiteConfiguration $websiteConfiguration,
        int $accountId
    ): void
    {
        $campaignConfigNames = $websiteConfiguration->getCampaignNames($accountId);

        foreach (array_slice($campaignConfigNames, 0, 3) as $campaignConfigName) {
            $campaignConfig = $websiteConfiguration->getCampaignConfig($accountId, (string)$campaignConfigName);

            if ($campaignConfig === null) {
                throw new \RuntimeException(
                    sprintf('Campaign config for account %d and campaign name %s not found.', $accountId, $campaignConfigName),
                );
            }

            $this->campaignSettingsFactory->create($campaignConfig);
        }
    }
}
