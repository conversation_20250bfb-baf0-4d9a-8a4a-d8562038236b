<?php

declare(strict_types=1);

namespace App\WebsiteSettings\EventSubscriber;

use App\Locale\Settings\LocaleSettingsHelperInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

readonly class InitLocaleEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                /**
                 * Priority needs to be higher than Symfony's LocaleListener::onKernelRequest(), otherwise setting the request locale has no
                 * effect on translations.
                 * {@see \Symfony\Component\HttpKernel\EventListener\LocaleListener::getSubscribedEvents()}
                 */
                ['setActiveLocale', 25],
            ],
        ];
    }

    public function setActiveLocale(RequestEvent $event): void
    {
        // Set website locale for this request
        $event->getRequest()->setLocale(
            $this->localeSettingsHelper->getSettings()->locale->code,
        );
    }
}
