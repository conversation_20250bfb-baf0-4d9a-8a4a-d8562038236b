<?php

declare(strict_types=1);

namespace App\TrademarkInfringement;

use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Search\Query\SearchQueryNormalizer;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Request\SeaRequestInterface;
use Visymo\ArrayReader\ArrayReader;

final readonly class TrademarkInfringementRuleMatcher
{
    private const string KEY_LOCALES    = 'locales';
    private const string KEY_QUERY      = 'query';
    private const string KEY_MATCH_TYPE = 'match_type';

    public function __construct(
        private SearchRequestInterface $searchRequest,
        private SearchQueryNormalizer $searchQueryNormalizer,
        private SeaRequestInterface $seaRequest,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function matchesRule(ArrayReader $ruleReader): bool
    {
        $ruleLocales = $ruleReader->getStringArray(self::KEY_LOCALES);

        if (!$this->matchesLocale($ruleLocales)) {
            return false;
        }

        $ruleQuery = mb_strtolower($ruleReader->getString(self::KEY_QUERY));
        $ruleWords = explode(' ', $ruleQuery);
        $ruleType = TrademarkInfringementMatchType::from($ruleReader->getString(self::KEY_MATCH_TYPE));

        $queries = [];

        if ($this->searchRequest->getQuery() !== null) {
            $queries[] = $this->searchRequest->getQuery();
        }

        if ($this->seaRequest->getReferrerAdCreative() !== null) {
            $queries[] = $this->searchQueryNormalizer->getNormalizedQuery(
                $this->seaRequest->getReferrerAdCreative(),
            );
        }

        foreach ($queries as $query) {
            $isMatch = $this->matchQuery(
                query    : $query,
                ruleQuery: $ruleQuery,
                ruleWords: $ruleWords,
                ruleType : $ruleType,
            );

            if ($isMatch) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param string[] $ruleWords
     */
    private function matchQuery(
        ?string $query,
        string $ruleQuery,
        array $ruleWords,
        TrademarkInfringementMatchType $ruleType
    ): bool
    {
        if ($query === null) {
            return false;
        }

        $query = mb_strtolower($query);

        return match ($ruleType) {
            TrademarkInfringementMatchType::EXACT  => $this->matchesSearchQueryExact($query, $ruleQuery, $ruleWords),
            TrademarkInfringementMatchType::PHRASE => $this->matchesSearchQueryPhrase($query, $ruleQuery, $ruleWords),
        };
    }

    /**
     * Checks if the query matches the rule query exactly without other words
     *
     * @param string[] $ruleWords
     */
    private function matchesSearchQueryExact(string $query, string $ruleQuery, array $ruleWords): bool
    {
        if ($query === $ruleQuery) {
            return true;
        }

        $queryNormalized = $this->searchQueryNormalizer->getNormalizedQueryWithoutSpecialCharacters($query);

        if ($queryNormalized === $ruleQuery) {
            return true;
        }

        $queryWords = explode(' ', $queryNormalized);
        sort($queryWords);
        sort($ruleWords);

        // Query contains the same words as the rule query
        if ($queryWords === $ruleWords) {
            return true;
        }

        $gluedRuleQuery = implode('', $ruleWords);

        return $queryNormalized === $gluedRuleQuery;
    }

    /**
     * Checks if the query matches the rule query as a phrase, with or without other words
     *
     * @param string[] $ruleWords
     */
    private function matchesSearchQueryPhrase(string $query, string $ruleQuery, array $ruleWords): bool
    {
        if ($query === $ruleQuery) {
            return true;
        }

        $queryNormalized = $this->searchQueryNormalizer->getNormalizedQueryWithoutSpecialCharacters($query);

        if ($queryNormalized === $ruleQuery) {
            return true;
        }

        $queryWords = explode(' ', $queryNormalized);
        $matchedWords = array_intersect($ruleWords, $queryWords);

        // Rule words are all present in the query
        if (count($matchedWords) === count($ruleWords)) {
            return true;
        }

        $gluedRuleQuery = implode('', $ruleWords);

        return in_array($gluedRuleQuery, $queryWords, true);
    }

    /**
     * @param string[] $ruleLocales
     */
    private function matchesLocale(array $ruleLocales): bool
    {
        if ($ruleLocales === []) {
            return true;
        }

        $locale = $this->localeSettingsHelper->getSettings()->locale->code;

        return in_array($locale, $ruleLocales, true);
    }
}
