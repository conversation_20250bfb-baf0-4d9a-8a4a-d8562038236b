<?php

declare(strict_types=1);

namespace App\GeoIp;

use App\AdBot\Request\AdBotRequestInterface;
use App\Debug\Request\DebugRequestInterface;
use App\GeoIp\Reader\GeoIp2CountryReader;
use App\GeoIp\Reader\GeoIp2CountryReaderFactory;
use App\Http\Request\Info\RequestInfoInterface;

class GeoIp2CountryHelper
{
    private const array EUROPEAN_COUNTRIES = [
        'AT',
        'BE',
        'BG',
        'CY',
        'CZ',
        'DE',
        'DK',
        'EE',
        'EL',
        'ES',
        'FI',
        'FR',
        'HR',
        'HU',
        'IE',
        'IT',
        'LT',
        'LU',
        'LV',
        'MT',
        'NL',
        'PL',
        'PT',
        'RO',
        'SE',
        'SI',
        'SK',
    ];

    private GeoIp2CountryReader $geoIpCountryReader;

    protected string $countryCode;

    public function __construct(
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly DebugRequestInterface $debugRequest,
        private readonly RequestInfoInterface $requestInfo,
        private readonly GeoIp2CountryReaderFactory $geoIpCountryReaderFactory
    )
    {
    }

    /**
     * Returns upper case country code for IP if possible, null otherwise
     *
     * @return string|null ISO-3166 alpha2 country code
     *
     * @link    http://www.geonames.org/countries/
     *
     * @example 'NL' or 'GB'
     */
    public function getVisitorCountryCode(): ?string
    {
        $countryCode = $this->debugRequest->getCountryCode();

        if ($countryCode !== null) {
            return $countryCode;
        }

        // Do not check for ad bots
        if ($this->adBotRequest->isAdBot()) {
            return null;
        }

        if (!isset($this->countryCode)) {
            $userIp = $this->requestInfo->getUserIp();
            $country = $userIp !== null ? $this->getGeoIpCountryReader()->country($userIp) : null;

            $this->countryCode = strtoupper((string)$country?->country->isoCode);
        }

        return $this->countryCode === '' ? null : $this->countryCode;
    }

    public function isEuVisitor(?bool $defaultValue = null): ?bool
    {
        $visitorCountryCode = $this->getVisitorCountryCode();

        if ($visitorCountryCode === null) {
            // return default value
            return $defaultValue;
        }

        return in_array($visitorCountryCode, self::EUROPEAN_COUNTRIES, true);
    }

    private function getGeoIpCountryReader(): GeoIp2CountryReader
    {
        if (!isset($this->geoIpCountryReader)) {
            $this->geoIpCountryReader = $this->geoIpCountryReaderFactory->create();
        }

        return $this->geoIpCountryReader;
    }
}
