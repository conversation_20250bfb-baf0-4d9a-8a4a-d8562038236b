<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Options;

use App\Ads\AdTestHelper;
use App\GoogleCsa\Parameter\GoogleCsaPublisherIdParameter;
use App\GoogleCsa\Parameter\GoogleCsaReferrerAdCreativeParameter;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Search\Request\SearchRequestInterface;
use Visymo\GoogleCsa\GoogleCsa;
use Visymo\GoogleCsa\GoogleCsaInterface;
use Visymo\GoogleCsa\Property\FunctionProperty;
use Visymo\GoogleCsa\Property\PropertyInterface;
use Visymo\GoogleCsa\RelatedSearch\RelatedSearchTargeting;
use Visymo\GoogleCsa\RelatedSearch\Unit\RelatedSearchUnit;

readonly class JavaScriptRelatedTermsPageOptions
{
    public function __construct(
        private GoogleCsaPublisherIdParameter $googleCsaPublisherIdParameter,
        private GoogleCsaReferrerAdCreativeParameter $googleCsaReferrerAdCreativeParameter,
        private LocaleSettingsHelperInterface $localeSettingsHelper,
        private AdTestHelper $adTestHelper
    )
    {
    }

    /**
     * @param string[] $relatedTerms
     *
     * @return PropertyInterface[]
     */
    public function createForContent(array $relatedTerms, string $resultsPageBaseUrl): array
    {
        $googleCsa = new GoogleCsa(
            $this->googleCsaPublisherIdParameter->getPublisherId(),
            null,
            $this->adTestHelper->isGoogleAdSenseTest(),
        );
        $googleCsa->relatedSearch()->setRelatedSearchTargeting(
            RelatedSearchTargeting::CONTENT,
        );

        $googleCsa
            ->setIvt(false)
            ->setTargetLanguage($this->localeSettingsHelper->getSettings()->locale->language->code)
            ->getCustomProperties()
            ->setProperty(
                FunctionProperty::create(
                    'ignoredPageParams',
                    '_vrtIgnoredPageParams',
                ),
                false,
            );

        $googleCsa->relatedSearch()
            ->setResultsPageBaseUrl($resultsPageBaseUrl)
            ->setResultsPageQueryParam(SearchRequestInterface::PARAMETER_QUERY)
            ->setReferrerAdCreative($this->googleCsaReferrerAdCreativeParameter->getReferrerAdCreativeForContent())
            ->setTerms($relatedTerms);

        $this->addDummyRelatedSearchUnit($googleCsa);

        return $googleCsa->getPageProperties();
    }

    /**
     * @return PropertyInterface[]
     */
    public function createForSearch(string $query, string $resultsPageBaseUrl): array
    {
        $googleCsa = new GoogleCsa(
            $this->googleCsaPublisherIdParameter->getPublisherId(),
            $query,
            $this->adTestHelper->isGoogleAdSenseTest(),
        );
        $googleCsa->relatedSearch()->setRelatedSearchTargeting(
            RelatedSearchTargeting::QUERY,
        );

        $googleCsa
            ->setIvt(false)
            ->setTargetLanguage($this->localeSettingsHelper->getSettings()->locale->language->code)
            ->getCustomProperties()
            ->setProperty(
                FunctionProperty::create(
                    'ignoredPageParams',
                    '_vrtIgnoredPageParams',
                ),
                false,
            );

        $googleCsa->relatedSearch()
            ->setResultsPageBaseUrl($resultsPageBaseUrl)
            ->setResultsPageQueryParam(SearchRequestInterface::PARAMETER_QUERY);

        $this->addDummyRelatedSearchUnit($googleCsa);

        return $googleCsa->getPageProperties();
    }

    private function addDummyRelatedSearchUnit(GoogleCsaInterface $googleCsa): void
    {
        // Dummy unit to enable related search page properties
        $googleCsa->relatedSearch()->addUnit(
            new RelatedSearchUnit('none', null, 4),
        );
    }
}
