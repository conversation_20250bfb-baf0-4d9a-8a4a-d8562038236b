<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Repository;

use App\AdBot\Request\AdBotRequestInterface;
use App\Brand\Settings\BrandSettingsHelper;
use App\JavaScriptRelatedTerms\Request\JavaScriptRelatedTermsViewRequestInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Visymo\AutosuggestApiClient\AutosuggestApiClientInterface;
use Visymo\AutosuggestApiClient\RelatedTerms\RelatedTerm;
use Visymo\AutosuggestApiClient\RelatedTerms\RelatedTermsRequest;

readonly class JavaScriptRelatedTermsRepository
{
    public function __construct(
        private JavaScriptRelatedTermsViewRequestInterface $javaScriptRelatedTermsViewRequest,
        private AdBotRequestInterface $adBotRequest,
        private LocaleSettingsHelperInterface $localeSettingsHelper,
        private SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private AutosuggestApiClientInterface $autosuggestApiClient,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    /**
     * @return string[]
     */
    public function getRelatedTerms(string $query, int $amount): array
    {
        $relatedTermsFromRequest = $this->javaScriptRelatedTermsViewRequest->getRelatedTerms();

        if ($relatedTermsFromRequest !== null) {
            // Apply max amount
            return array_slice(
                $relatedTermsFromRequest,
                0,
                $amount,
            );
        }

        return $this->fetchFromAutosuggestApi($query, $amount);
    }

    /**
     * @return string[]
     */
    private function fetchFromAutosuggestApi(string $query, int $amount): array
    {
        $relatedTermsRequest = RelatedTermsRequest::create(
            query           : $query,
            locale          : $this->localeSettingsHelper->getSettings()->locale->code,
            limit           : $amount,
            robotAgent      : $this->adBotRequest->isAdBot(),
            brandSlug       : $this->brandSettingsHelper->getSettings()->getSlug(),
            splitTestVariant: $this->splitTestExtendedReader->getVariant(),
        );

        $relatedTermsResponse = $this->autosuggestApiClient->getRelatedTerms($relatedTermsRequest);

        return array_map(
            static fn (RelatedTerm $relatedTerm) => $relatedTerm->value,
            $relatedTermsResponse->relatedTerms,
        );
    }
}
