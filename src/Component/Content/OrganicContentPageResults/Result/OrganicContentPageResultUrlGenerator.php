<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults\Result;

use App\Brand\Settings\BrandSettingsHelper;
use App\ContentPage\Helper\ContentPageHelper;
use App\ContentPage\Organic\OrganicContentPageDomain;
use App\ContentPage\Repository\OrganicContentPageDomainRepository;
use App\ContentPage\Request\ContentPageRequestInterface;
use App\Debug\Request\DebugRequestInterface;
use App\Http\Request\GenericRequestInterface;
use App\Http\Url\DevelopHostHelper;
use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\Locale\Request\LocaleRequestInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Request\SeaRequestInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

readonly class OrganicContentPageResultUrlGenerator
{
    public function __construct(
        private GenericRequestInterface $genericRequest,
        private DebugRequestInterface $debugRequest,
        private OrganicContentPageDomainRepository $organicContentPageDomainRepository,
        private DevelopHostHelper $developHostHelper,
        private RouterInterface $router,
        private ContentPageHelper $contentPageHelper,
        private LocaleRequestInterface $localeRequest,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private LoggerInterface $logger,
        private JsonTemplateRequestInterface $jsonTemplateRequest,
        private BrandSettingsHelper $brandSettingsHelper,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function generate(
        ?string $query,
        ContentPage $contentPage,
        ?bool $linkToActiveBrand
    ): ?string
    {
        if ($this->debugRequest->forceMockSearch()) {
            return 'https://content-page.mockup-result.com/dsr';
        }

        // Use website settings locale as fallback, otherwise we cannot generate a URL for online brands.
        $locale = $this->localeRequest->getLocale()
                  ?? $this->localeSettingsHelper->getSettings()->locale->code;

        $parameters = [
            SearchRequestInterface::PARAMETER_QUERY                       => $query,
            ContentPageRequestInterface::PARAMETER_CONTENT_PAGE_PUBLIC_ID => $contentPage->publicId,
            ClickIdSource::VISYMO_CLICK_ID->value                         => $this->genericRequest->getPageviewId(),
            LocaleRequestInterface::PARAMETER_LOCALE                      => $locale,
            SeaRequestInterface::PARAMETER_REFERRER_AD_CREATIVE           => $contentPage->title,
        ];

        if ($linkToActiveBrand === true) {
            $parameters[JsonTemplateRequestInterface::PARAMETER_TEMPLATE_VARIANT] = $this->jsonTemplateRequest->getTemplateVariant();

            return $this->router->generate(
                'route_display_search_related',
                $parameters,
                UrlGeneratorInterface::ABSOLUTE_URL,
            );
        }

        try {
            $organicContentPageDomain = $this->getDomain($contentPage->collectionSlug, $linkToActiveBrand);
        } catch (OrganicContentPageDomainNotFoundException $exception) {
            $this->logger->notice($exception->getMessage(), ['exception' => $exception]);

            return null;
        }

        if ($organicContentPageDomain->route === 'route_article') {
            unset($parameters[ContentPageRequestInterface::PARAMETER_CONTENT_PAGE_PUBLIC_ID]);

            $url = sprintf(
                'https://%s/article/%s-%u/?%s',
                $organicContentPageDomain->domain,
                $contentPage->slug,
                $contentPage->publicId,
                http_build_query($parameters),
            );
        } elseif ($organicContentPageDomain->route === 'route_content_search') {
            $url = sprintf(
                'https://%s/cs/%s-%u/?%s',
                $organicContentPageDomain->domain,
                $contentPage->slug,
                $contentPage->publicId,
                http_build_query($parameters),
            );
        } elseif ($organicContentPageDomain->route === 'route_microsoft_search') {
            $url = sprintf(
                'https://%s/ms?%s',
                $organicContentPageDomain->domain,
                http_build_query($parameters),
            );
        } else {
            $url = sprintf(
                'https://%s/dsr?%s',
                $organicContentPageDomain->domain,
                http_build_query($parameters),
            );
        }

        return $this->developHostHelper->addDevelopToUrl($url);
    }

    /**
     * @throws OrganicContentPageDomainNotFoundException
     */
    private function getDomain(string $contentPageCollection, ?bool $linkToActiveBrand): OrganicContentPageDomain
    {
        $locale = $this->localeSettingsHelper->getSettings()->locale->code;
        $device = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->device;
        $excludeBrand = $linkToActiveBrand === false
            ? $this->brandSettingsHelper->getSettings()->getSlug()
            : null;
        $brands = $this->contentPageHelper->getBrandsByCollectionAndDevice(
            collection: $contentPageCollection,
            device    : $device,
        );

        $organicContentPageDomain = $this->organicContentPageDomainRepository->findOneRandomByLocaleAndBrand(
            locale       : $locale,
            includeBrands: $brands,
            excludeBrand : $excludeBrand,
        );

        // Try last time as fallback if needed, this time without device filter.
        $organicContentPageDomain ??= $this->organicContentPageDomainRepository->findOneRandomByLocaleAndBrand(
            locale       : $locale,
            includeBrands: $this->contentPageHelper->getBrandsByCollection($contentPageCollection),
            excludeBrand : $excludeBrand,
        );

        if ($organicContentPageDomain === null) {
            throw OrganicContentPageDomainNotFoundException::create($locale);
        }

        return $organicContentPageDomain;
    }
}
