<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults;

use App\AdBot\Request\AdBotRequestInterface;
use App\Brand\Settings\BrandSettingsHelper;
use App\ContentPage\Repository\OrganicContentPageDomainRepository;
use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\JsonTemplate\Component\ComponentInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Search\Request\SearchRequestInterface;

readonly class OrganicContentPageResultsSupport
{
    public function __construct(
        private LocaleSettingsHelperInterface $localeSettingsHelper,
        private OrganicContentPageDomainRepository $organicContentPageDomainRepository,
        private SearchRequestInterface $searchRequest,
        private AdBotRequestInterface $adBotRequest,
        private DisplaySearchRelatedSettings $displaySearchRelatedSettings,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function isSupported(ComponentInterface $component): bool
    {
        if (!$component instanceof OrganicContentPageResultsComponentInterface) {
            return false;
        }

        $locale = $this->localeSettingsHelper->getSettings()->locale->code;

        // Locale is required
        if (!$this->organicContentPageDomainRepository->hasLocale($locale)) {
            return false;
        }

        // Disable organic content page results for ad bot requests on "/ws"
        if ($this->adBotRequest->isAdBot() && $this->searchRequest->isWebSearch()) {
            return false;
        }

        if ($component->linkToActiveBrand() === true) {
            return $this->displaySearchRelatedSettings->enabled;
        }

        if ($component->linkToActiveBrand() === false) {
            $brandSlug = $this->brandSettingsHelper->getSettings()->getSlug();

            return $this->organicContentPageDomainRepository->findOneRandomByLocale($locale, $brandSlug) !== null;
        }

        return true;
    }
}
