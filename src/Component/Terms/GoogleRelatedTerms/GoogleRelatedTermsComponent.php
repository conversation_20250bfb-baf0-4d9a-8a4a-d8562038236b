<?php

declare(strict_types=1);

namespace App\Component\Terms\GoogleRelatedTerms;

use App\Component\Terms\RelatedTerms\RelatedTermsComponent;
use App\JsonTemplate\Component\AbstractComponent;

final class GoogleRelatedTermsComponent extends AbstractComponent
{
    public function __construct(
        public readonly int $amount,
        public readonly ?string $route,
        public readonly GoogleRelatedTermsTarget $target,
        public readonly bool $termsUrlParameterEnabled,
        public readonly ?string $containerSuffix,
        public ?RelatedTermsComponent $fallbackRelatedTerms
    )
    {
    }

    public static function getType(): string
    {
        return 'google_related_terms';
    }

    public function getRenderer(): string
    {
        return GoogleRelatedTermsRenderer::class;
    }
}
