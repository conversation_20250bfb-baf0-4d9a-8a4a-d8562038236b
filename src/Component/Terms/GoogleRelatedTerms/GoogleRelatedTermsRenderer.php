<?php

declare(strict_types=1);

namespace App\Component\Terms\GoogleRelatedTerms;

use App\Component\Terms\RelatedTerms\RelatedTermsComponent;
use App\Component\Terms\RelatedTerms\RelatedTermsRenderer;
use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\GoogleCsa\Generator\GoogleRelatedTermsContainerGeneratorInterface;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\GoogleCsaRelatedSearchUnitViewDataRequest;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Monetization\Settings\MonetizationSettings;
use App\SearchApi\SearchApiManager;
use App\SplitTest\SplitTestExtendedReaderInterface;

final class GoogleRelatedTermsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly SearchApiManager $searchApiManager,
        private readonly GoogleCsaRegistry $googleCsaRegistry,
        private readonly DisplaySearchRelatedSettings $displaySearchRelatedSettings,
        private readonly RelatedTermsRenderer $relatedTermsRenderer,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly MonetizationSettings $monetizationSettings,
        private readonly GoogleRelatedTermsContainerGeneratorInterface $googleRelatedTermsContainerGenerator
    )
    {
    }

    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        $componentViewDataRequest = $view->getDataRequest()
            ->createForComponent($component)
            ->setRequestConditions($conditions);

        $this->buildRequest($component, $componentViewDataRequest, $conditions);
        $this->registerDataRequirements($component, $componentViewDataRequest);

        $componentViewDataRequest->finalize($view->getDataRegistry());

        $fallbackRelatedTermsComponent = $this->getFallbackRelatedTermsComponent($component);

        if ($fallbackRelatedTermsComponent !== null) {
            $this->relatedTermsRenderer->build($fallbackRelatedTermsComponent, $view, $conditions);
        }
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        if (!$component->target->forContent()) {
            $request->setRequirements([ViewDataProperty::QUERY]);
        }

        if ($this->requiresVisymoRelatedTerms($component)) {
            $request->setRequirements([ViewDataProperty::RELATED_TERMS]);
        }
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        $request->googleCsa()->addRelatedSearchUnit(
            new GoogleCsaRelatedSearchUnitViewDataRequest(
                amount                  : $component->amount,
                component               : $component,
                containerSuffix         : $component->containerSuffix,
                route                   : $component->route,
                forContent              : $component->target->forContent(),
                addVisymoRelatedTerms   : $this->shouldAddVisymoRelatedTermsToGoogleCsaRequest($component),
                termsUrlParameterEnabled: $component->termsUrlParameterEnabled,
            ),
        );

        if (!$this->requiresVisymoRelatedTerms($component)) {
            return;
        }

        $relatedTermsViewDataRequest = $request->relatedTerms()->increaseAmount($component->amount);

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::RELATED_TERMS,
            searchApiViewDataRequest: $relatedTermsViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        $container = $this->googleRelatedTermsContainerGenerator->generateContainerForComponent(
            component: $component,
            suffix   : $component->containerSuffix
        );

        $relatedSearchUnit = $this->googleCsaRegistry->getGoogleCsa()?->relatedSearch()
            ->getUnitByContainer($container);

        if ($relatedSearchUnit === null) {
            return '';
        }

        $html = $relatedSearchUnit->getContainerHtml('component component--no-default-space csa csa--loading gr-results');

        $fallbackRelatedTermsComponent = $this->getFallbackRelatedTermsComponent($component);

        if ($fallbackRelatedTermsComponent !== null) {
            $fallbackRelatedTermsComponent->setFallbackComponentClass(sprintf('fallback-%s', $container));

            $html .= $this->relatedTermsRenderer->render($fallbackRelatedTermsComponent, $view);
        }

        return $html;
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        if ($component->fallbackRelatedTerms === null) {
            return '';
        }

        return $this->relatedTermsRenderer->renderHeaders($component->fallbackRelatedTerms, $view);
    }

    /**
     * {@see GoogleCsaRelatedSearchRequestFactory::getRelatedTerms}
     */
    private function requiresVisymoRelatedTerms(GoogleRelatedTermsComponent $component): bool
    {
        if (!$this->monetizationSettings->relatedTermsEnabled) {
            return false;
        }

        if ($this->getFallbackRelatedTermsComponent($component) !== null) {
            return true;
        }

        if ($component->target->requiresVisymoRelatedTerms()) {
            return true;
        }

        return false;
    }

    private function shouldAddVisymoRelatedTermsToGoogleCsaRequest(GoogleRelatedTermsComponent $component): bool
    {
        $requiresVisymoRelatedTerms = $this->requiresVisymoRelatedTerms($component);

        if (!$requiresVisymoRelatedTerms) {
            return false;
        }

        if ($this->splitTestExtendedReader->isVariantActive('nrt')) {
            return false;
        }

        return true;
    }

    private function getFallbackRelatedTermsComponent(GoogleRelatedTermsComponent $component): ?RelatedTermsComponent
    {
        return $this->displaySearchRelatedSettings->relatedFallbackEnabled
            ? $component->fallbackRelatedTerms
            : null;
    }
}
