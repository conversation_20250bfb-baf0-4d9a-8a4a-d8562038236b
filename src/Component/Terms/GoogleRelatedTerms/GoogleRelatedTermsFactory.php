<?php

declare(strict_types=1);

namespace App\Component\Terms\GoogleRelatedTerms;

use App\Component\Terms\RelatedTerms\RelatedTermsComponent;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

final readonly class GoogleRelatedTermsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return GoogleRelatedTermsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        /** @var RelatedTermsComponent|null $fallbackRelatedTerms */
        $fallbackRelatedTerms = $componentFactory->createNullableFromOptions(
            $options[GoogleRelatedTermsResolver::KEY_FALLBACK_RELATED_TERMS] ?? null,
        );

        return new GoogleRelatedTermsComponent(
            amount                  : $options[GoogleRelatedTermsResolver::KEY_AMOUNT],
            route                   : $options[GoogleRelatedTermsResolver::KEY_ROUTE],
            target                  : GoogleRelatedTermsTarget::from($options[GoogleRelatedTermsResolver::KEY_TARGET]),
            termsUrlParameterEnabled: $options[GoogleRelatedTermsResolver::KEY_TERMS_URL_PARAMETER_ENABLED],
            containerSuffix         : $options[GoogleRelatedTermsResolver::KEY_CONTAINER_SUFFIX],
            fallbackRelatedTerms    : $fallbackRelatedTerms,
        );
    }
}
