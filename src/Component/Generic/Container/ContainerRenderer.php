<?php

declare(strict_types=1);

namespace App\Component\Generic\Container;

use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Parent\AbstractComponentParentRenderer;
use App\JsonTemplate\View\ViewInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Template\DelayedContainer\DelayedContainerHelper;

final class ContainerRenderer extends AbstractComponentParentRenderer
{
    public function __construct(
        private readonly HeaderLinkRegistry $headerLinkRegistry,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly DelayedContainerHelper $delayedContainerHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function getComponents(ComponentInterface $component): array
    {
        if (!$component instanceof ContainerComponent) {
            throw UnsupportedComponentException::create($component, [ContainerComponent::class]);
        }

        return $component->components;
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContainerComponent) {
            throw UnsupportedComponentException::create($component, [ContainerComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'view'                          => $view,
                'layout'                        => $component->layout->value,
                'mode'                          => $component->mode?->value,
                'components'                    => $component->components,
                'show_delayed_container_loader' => $this->delayedContainerHelper->showDelayedContainerLoader(),
            ],
        );
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContainerComponent) {
            throw UnsupportedComponentException::create($component, [ContainerComponent::class]);
        }

        $output = $this->renderFontHeader($component);
        $output .= $this->renderThemeColorHeader($component);
        $output .= parent::renderHeaders($component, $view);

        return $output;
    }

    private function renderFontHeader(ContainerComponent $component): string
    {
        if ($component->font === null) {
            return '';
        }

        foreach ($component->font->getHeaderLinks() as $headerLink) {
            $this->headerLinkRegistry->add($headerLink);
        }

        return $this->twig->render(
            $component->font->getTwigTemplate(),
            [
                'font' => $component->font->value,
            ],
        );
    }

    private function renderThemeColorHeader(ContainerComponent $component): string
    {
        if ($this->splitTestExtendedReader->isVariantActive('drkt')) {
            $color = '#101218';
        } else {
            $color = $component->mode === ContainerMode::DARK
                ? '#01074B'
                : '#FFFFFF';
        }

        return $this->twig->render(
            '@component/Generic/Container/theme/theme_color.html.twig',
            [
                'color' => $color,
            ],
        );
    }
}
