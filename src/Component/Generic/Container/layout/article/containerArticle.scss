@import "../stickyHeader";

// stylelint-disable max-line-length
// stylelint-disable visymo/sort-properties-alphabetically
// stylelint-disable plugin/selector-bem-pattern

/** @define container */
.container--article {
    --container__column-three_width: 30rem;
    --container__column-one_width: 30rem;
    --container__columns_grid-template-areas: ". one two three .";
    // stylelint-disable-next-line prettier/prettier
    --container__columns_grid-template-columns: auto minmax(10rem, var(--container__column-one_width)) minmax(10rem, var(--container__column-two_width)) minmax(10rem, var(--container__column-three_width)) auto;
    --container__column-two_width: 70rem;

    @media #{map-get($media-max, c)} {
        --container__columns_grid-template-areas: "one" "two" "three";
        --container__columns_grid-template-columns: 1fr;
    }

    .section {
        &--footer {
            --container__section_background: #f1f3f4;
        }

        &--search {
            --container__section_background: #ffffff;
            --container__section_box-shadow:
                0 0.4rem 0.8rem -0.2rem rgba(16, 24, 40, 0.1),
                0 0.2rem 0.4rem -0.2rem rgba(16, 24, 40, 0.06);
        }

        &--search-category {
            --container__section_background: #ffffff;
            --container__section_box-shadow:
                0 0.4rem 0.8rem -0.2rem rgba(16, 24, 40, 0.1),
                0 0.2rem 0.4rem -0.2rem rgba(16, 24, 40, 0.06);
        }
    }
}

.container--article {
    background: var(--container__background, none);
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    @media #{map-get($media-min, c)} {
        /* stylelint-disable-next-line selector-class-pattern */
        .columns__column--two {
            height: max-content;
            position: sticky;
            left: 0;
            top: 7.3rem;
        }
    }

    @include sticky-header;
}

/** @define html */
.html--article {
    &.html--mode-dark {
        --container__background: #01074b;
        --container__section_box-shadow: none;
        --container__section_color: #ffffff;
        --container__section-highlight_color: var(--brand-primary-color);
        --container__section-secondary_color: #dddddd;
        --container__delayed-content-loader__background: #010970;

        .section {
            &--search {
                --container__section_background: #01074b;
            }

            &--search-category {
                --container__section_background: #0f176b;
                --container__section_box-shadow: 0 0.2rem 0.4rem 0 rgba(16, 24, 40, 0.3);
            }
        }
    }
}
