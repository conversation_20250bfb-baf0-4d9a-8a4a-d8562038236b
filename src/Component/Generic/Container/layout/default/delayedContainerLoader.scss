/** @define csa */
.csa--loading {
    min-height: 20rem;
}

/** @define component */
// stylelint-disable declaration-no-important
// stylelint-disable plugin/selector-bem-pattern
.component[id^="delayed"] {
    background: none !important;
    border-color: transparent !important;
    border-radius: 0.5rem;
    box-shadow: none !important;
    overflow: hidden;
    position: relative;
    visibility: visible !important;

    &::after {
        background: var(--container__delayed-content-loader__background, #eeeeee);
        border-radius: 0.5rem;
        bottom: 0;
        content: "";
        display: block;
        left: 1.5rem;
        position: absolute;
        right: 1.5rem;
        top: 0;
        z-index: 2;
    }

    * {
        visibility: hidden;
    }
}
