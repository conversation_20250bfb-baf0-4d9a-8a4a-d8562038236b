<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPage;

use App\Brand\Settings\BrandSettingsHelper;
use App\Domain\Settings\DomainSettingsHelperInterface;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use Twig\Environment;

final class InfoPageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly DomainSettingsHelperInterface $domainSettingsHelper,
        private readonly LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof InfoPageComponent) {
            throw UnsupportedComponentException::create($component, [InfoPageComponent::class]);
        }

        $content = InfoPageContent::from($component->content);
        $language = $this->localeSettingsHelper->getSettings()->locale->language->code;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'content'                   => $component->content,
                'brand_name'                => $this->brandSettingsHelper->getSettings()->getName(),
                'host'                      => $this->domainSettingsHelper->getSettings()->host,
                'template'                  => $content->template($language),
                'page_title_translation_id' => $content->getPageTitleTranslationId(),
            ],
        );
    }
}
