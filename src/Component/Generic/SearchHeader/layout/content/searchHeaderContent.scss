@import "../default/searchHeaderMixins";

/** @define search-header */
// stylelint-disable visymo/sort-properties-alphabetically
.search-header--content-1 {
    position: relative;

    @include search-header-search-bar-mobile-full-page;

    .search-header {
        // Rows
        &__row-menu {
            display: none;
        }

        &__row-search {
            column-gap: 1rem;
            display: grid;
            grid-template-areas: "logo search";
            padding-bottom: 1.5rem;
            padding-top: 1.5rem;
        }

        // Area
        &__area {
            display: none;
        }

        // Auto suggest
        &__auto-suggest {
            top: 100%;
        }

        // Brand logo
        &__logo {
            align-self: center;
            grid-area: logo;
        }

        &__brand-image {
            height: max-content;
            max-height: 4.4rem;
            max-width: 20rem;
            object-fit: scale-down;
        }

        &__brand-icon {
            display: none;
        }

        &__brand-link {
            display: inline-block;
        }
    }

    // Device responsive specific
    @media #{map-get($media-max, b)} {
        .search-header {
            // Switch brand logo with favicon
            &__brand-image {
                display: none;
            }

            &__brand-icon {
                display: block;
            }
        }
    }

    /* stylelint-disable-next-line plugin/selector-bem-pattern */
    .search-bar--default {
        --padding: 0;
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 5rem;
        --field_box-shadow: none;
        grid-area: search;
        max-width: 68rem;
        place-self: center end;
        width: 100%;

        @media #{map-get($media-max, c)} {
            // stylelint-disable selector-class-pattern
            // stylelint-disable plugin/selector-bem-pattern
            .search-bar__field-button {
                background-color: var(--field-button_background-color, var(--brand-primary-color));
                color: var(--field-button-highlight_color, #ffffff);
            }

            // stylelint-enable selector-class-pattern
            // stylelint-enable plugin/selector-bem-pattern
        }
    }
}
