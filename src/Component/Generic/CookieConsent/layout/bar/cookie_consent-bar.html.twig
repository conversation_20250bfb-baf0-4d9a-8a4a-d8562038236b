{# @var cookie_brand_name string #}
{# @var cookie_info_page string #}
{# @var layout string #}

{{ component_style(['cookieConsentBar']) }}

{% set component_class = 'cookie-consent' %}

<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__container">
        <div class="{{ component_class }}__container-inner">
            <div class="{{ component_class }}__content">
                <div class="{{ component_class }}__message">
                    {{ 'cookie_consent.message'|trans({"%brand%": cookie_brand_name|escape})|raw }}
                </div>
                <div class="{{ component_class }}__options">
                    <a class="{{ component_class }}__more-info" target="_blank" rel="nofollow noopener noreferrer" href="{{ cookie_info_page }}">{{ 'cookie_consent.more_info'|trans }}</a>
                    <a id="{{ component_class }}__accept" class="{{ component_class }}__accept" target="_blank">{{ 'cookie_consent.accept'|trans }}</a>
                </div>
            </div>
        </div>
    </div>
</div>
