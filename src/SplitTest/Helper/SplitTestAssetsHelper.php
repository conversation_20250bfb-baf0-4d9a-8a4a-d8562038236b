<?php

declare(strict_types=1);

namespace App\SplitTest\Helper;

use App\Assets\AssetsHelper;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;

final readonly class SplitTestAssetsHelper
{
    public function __construct(
        private SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private AssetsHelper $assetsHelper,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function getSplitTestStyle(): ?string
    {
        $splitTestVariant = $this->splitTestExtendedReader->getVariant();

        if ($splitTestVariant === null) {
            return null;
        }

        return $this->assetsHelper->getSplitTestCss(
            $splitTestVariant,
            $this->localeSettingsHelper->getSettings()->locale->language->isRightToLeft,
        );
    }

    public function getSplitTestJs(): ?string
    {
        $splitTestVariant = $this->splitTestExtendedReader->getVariant();

        if ($splitTestVariant === null) {
            return null;
        }

        return $this->assetsHelper->getSplitTestJs($splitTestVariant);
    }
}
