<?php

declare(strict_types=1);

namespace App\Locale\Settings;

use App\Locale\Model\Locale;

final readonly class LocaleSettings
{
    public const string KEY_LOCALE     = 'locale';
    public const string KEY_IS_DEFAULT = 'is_default';

    public function __construct(
        public Locale $locale,
        public bool $isDefault
    )
    {
    }

    /**
     * @return array<string, string|boolean>
     */
    public function toArray(): array
    {
        return [
            self::KEY_LOCALE     => $this->locale->code,
            self::KEY_IS_DEFAULT => $this->isDefault,
        ];
    }
}
