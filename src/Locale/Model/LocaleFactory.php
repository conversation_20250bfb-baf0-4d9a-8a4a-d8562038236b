<?php

declare(strict_types=1);

namespace App\Locale\Model;

use App\Locale\Exception\InvalidLocaleException;

final class LocaleFactory
{
    private const string LOCALE_PATTERN = '~^([a-z]{2})_([A-Z]{2})$~';

    public function create(string $code): Locale
    {
        if (preg_match(self::LOCALE_PATTERN, $code, $matches) !== 1) {
            throw InvalidLocaleException::create($code);
        }

        return new Locale(
            code    : $code,
            isoCode : strtolower(str_replace('_', '-', $code)),
            language: new Language($matches[1]),
            region  : new Region($matches[2]),
        );
    }
}
