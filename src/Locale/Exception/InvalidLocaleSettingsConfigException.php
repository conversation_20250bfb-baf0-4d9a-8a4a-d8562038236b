<?php

declare(strict_types=1);

namespace App\Locale\Exception;

use App\WebsiteSettings\Settings\Exception\InvalidWebsiteSettingsConfigException;

class InvalidLocaleSettingsConfigException extends InvalidWebsiteSettingsConfigException
{
    public static function create(\Throwable $previous): self
    {
        return new self(
            sprintf('Invalid locale settings config given: %s', $previous->getMessage()),
            0,
            $previous,
        );
    }
}
