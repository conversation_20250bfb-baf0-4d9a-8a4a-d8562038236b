<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\Tests\Integration\ConversionTracking;

use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Conversion\ConversionRelatedType;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\Generic\Device\Device;
use App\SplitTest\Activate\ActiveSplitTest;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficSource;
use App\WebsiteSettings\Settings\ConversionTracking\ConversionTrackingOrderType;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\ConversionTracking\TrackingOrder\Type\RandomTrackingOrderFactoryStub;
use Tests\Stub\Domain\Settings\DomainSettingsStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;
use Visymo\Shared\Infrastructure\Stub\Domain\Logger\MemoryLoggerStub;

class WhiteLabelConversionTrackingIntegrationTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private MemoryLoggerStub $memoryLoggerStub;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        /** @var MemoryLoggerStub $memoryLoggerStub */
        $memoryLoggerStub = self::getContainer()->get('test.monolog.logger.conversion');
        $this->memoryLoggerStub = $memoryLoggerStub;

        /** @var DateTimeFactoryStub $dateTimeFactoryStub */
        $dateTimeFactoryStub = self::getContainer()->get(DateTimeFactory::class);
        $dateTimeFactoryStub->setDateTime(
            new \DateTime('08-05-2023 12:08:00', TimezoneEnum::UTC->toDateTimeZone()),
        );

        $activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('ipad')
            ->setClickId(new ClickId('test', ClickIdSource::GOOGLE_CLICK_ID))
            ->setActiveSplitTest(new ActiveSplitTest(1, 'variant_a'))
            ->setCampaignId(123)
            ->setCampaignName('campaign-name')
            ->setDevice(Device::DESKTOP)
            ->setPublisher('publisher')
            ->setAdditionalChannels(['kwc_123', 'kwc_128'])
            ->setTrafficSource(TrafficSource::GOOGLE)
            ->setNetwork(Network::GOOGLE_SEARCH)
            ->setAccountId(31723)
            ->setAdGroupId(46055)
            ->setKeywordId(59942)
            ->setGoogleLocationId('214132-dfsgsfd-342')
            ->setGenericSecondaryClickId('generic-secondary-click-id')
            ->setCustomId('custom_id')
            ->setConversionRoute('route_web_search')
            ->create();
        self::getContainer()->set(
            ActiveTrackingEntryHelperInterface::class,
            $activeTrackingEntryHelperStub,
        );

        /** @var RandomTrackingOrderFactoryStub $randomTrackingOrderFactoryStub */
        $randomTrackingOrderFactoryStub = self::getContainer()->get(RandomTrackingOrderFactoryStub::class);
        $randomTrackingOrderFactoryStub->setRandomTrackingOrderId('random-tracking-order-id');

        $websiteSettingsStub = $this->stubs()->websiteSettings();
        $websiteSettingsStub->getGoogleAdSense()->setEnabled(true)
            ->setContractType(ContractType::DIRECT);
        $websiteSettingsStub->getConversionTracking()->setOrderType(ConversionTrackingOrderType::DEFAULT);

        $this->stubs()->domainSettingsHelper()
            ->setSettings(
                (new DomainSettingsStubBuilder())
                    ->setHost('id.brand.com')
                    ->create(),
            );

        $this->stubs()->localeSettingsHelper()
            ->setLocale('en_US');

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setSlug('visymo');
        $brandSettingsStub->setPartnerSlug('partner');

        self::websiteSettingsTestHelper()->injectFoodBrandWebsiteConfiguration();
        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        self::brandSettingsTestHelper()->injectBrandSettings($brandSettingsStub);
    }

    /**
     * @return mixed[]
     */
    public static function conversionDataProvider(): array
    {
        $defaultUrlParameters = [
            'q'     => 'ipad',
            'astid' => '123',
            'acid'  => '456',
            'arq'   => 'test_arq',
            'vid'   => '1234abcd-1234-1234-1234-abcd12345678',
            'hhc'   => 'https://id.brand.nl',
            'tv'    => 'clt',
        ];

        return [
            'bing ads'                 => [
                'path'          => '/tp/ba',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'block' => 1,
                    'rank'  => 2,
                ],
                'eventType'     => ConversionEventType::CLICK_AD->value,
                'extraLogData'  => [
                    ConversionLogExtra::AD_TYPE                  => 'bing',
                    ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => 1,
                ],
            ],
            'google ad manager'        => [
                'path'          => '/tp/gamd',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'aup' => '/28456428/display-smart-banner/29628',
                ],
                'eventType'     => ConversionEventType::DISPLAY_AD->value,
                'extraLogData'  => [
                    ConversionLogExtra::AD_TYPE => 'google_ad_manager',
                ],
            ],
            'google adsense'           => [
                'path'          => '/tp/ga',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'block' => 1,
                    'ad'    => 3,
                ],
                'eventType'     => ConversionEventType::CLICK_AD->value,
                'extraLogData'  => [
                    ConversionLogExtra::AD_TYPE                  => 'google',
                    ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => 1,
                ],
            ],
            'google adsense online'    => [
                'path'                      => '/tp/gao',
                'urlParameters'             => $defaultUrlParameters,
                'eventType'                 => ConversionEventType::CLICK_AD->value,
                'extraLogData'              => [
                    ConversionLogExtra::AD_TYPE                  => 'google',
                    ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => 1,
                ],
                'googleAdSenseContractType' => ContractType::ONLINE,
            ],
            'google related terms'     => [
                'path'          => '/tp/grt',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'block' => 1,
                    'ad'    => 3,
                ],
                'eventType'     => ConversionEventType::CLICK_RELATED->value,
                'extraLogData'  => [
                    ConversionLogExtra::CONVERSION_RELATED_TYPE => ConversionRelatedType::GOOGLE_ADSENSE,
                ],
            ],
            'pageview landing'         => [
                'path'          => '/tp/pvl',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'block' => 1,
                    'ad'    => 3,
                ],
                'eventType'     => ConversionEventType::PAGEVIEW_LANDING->value,
            ],
            'pageview landing related' => [
                'path'          => '/tp/pvlr',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'block' => 1,
                    'ad'    => 3,
                ],
                'eventType'     => ConversionEventType::PAGEVIEW_LANDING_RELATED->value,
            ],
            'submit search'            => [
                'path'          => '/tp/ss',
                'urlParameters' => $defaultUrlParameters,
                'eventType'     => ConversionEventType::SUBMIT_SEARCH->value,
                'expectLog'     => false,
            ],
            'visymo related terms'     => [
                'path'          => '/tp/vrt',
                'urlParameters' => [
                    ...$defaultUrlParameters,
                    'block' => 1,
                    'ad'    => 3,
                ],
                'eventType'     => ConversionEventType::CLICK_RELATED->value,
                'extraLogData'  => [
                    ConversionLogExtra::PAGE_TERM               => null,
                    ConversionLogExtra::CONVERSION_RELATED_TYPE => ConversionRelatedType::VISYMO,
                ],
            ],
        ];
    }

    /**
     * @param array<string, mixed> $urlParameters
     * @param array<string, mixed> $extraLogData
     */
    #[DataProvider('conversionDataProvider')]
    public function testConversion(
        string $path,
        array $urlParameters,
        string $eventType,
        array $extraLogData = [],
        ContractType $googleAdSenseContractType = ContractType::DIRECT,
        bool $expectLog = true
    ): void
    {
        $this->stubs()->websiteSettings()->getGoogleAdSense()->setContractType($googleAdSenseContractType);
        $request = AbstractBrandWebsitesIntegrationTestCase::createRequest(
            path      : $path,
            parameters: $urlParameters,
            headers   : [
                            'Accept-Language'            => 'en-us,en;q=0.5',
                            'REMOTE_ADDR'                => '**************', // NL
                            'X-Loadbalancer-TLS-Version' => '1.2', // TLS 1.2
                        ],
        );

        $this->handleRequest($request);

        static::assertSame(
            $expectLog ? $this->getExpectedLogData($eventType, $extraLogData) : [],
            $this->memoryLoggerStub->getNormalizedLogs(),
        );
    }

    /**
     * @param array<string, mixed> $extraLogData
     *
     * @return array<string, mixed>
     */
    private function getExpectedLogData(
        string $eventType,
        array $extraLogData
    ): array
    {
        $adStyleId = match ($eventType) {
            ConversionEventType::CLICK_AD->value,
            ConversionEventType::CLICK_RELATED->value => 123,
            default                                   => null,
        };
        $adClientId = match ($eventType) {
            ConversionEventType::CLICK_AD->value,
            ConversionEventType::CLICK_RELATED->value => '456',
            default                                   => null,
        };

        return [
            'info' => [
                [
                    'message' => 'conversion_log',
                    'context' => [
                        'event_type'          => $eventType,
                        'oid'                 => 'random-tracking-order-id',
                        'oid_type'            => 'random',
                        'brand_slug'          => 'visymo',
                        'partner_slug'        => 'partner',
                        'q'                   => 'ipad',
                        'arq'                 => 'test_arq',
                        'pageview_id'         => null,
                        'visit_id'            => '1234abcd-1234-1234-1234-abcd12345678',
                        'conversion_route'    => 'route_web_search',
                        'lander_query'        => 'ipad',
                        'cid'                 => 123,
                        'asid'                => 'campaign-name',
                        'de'                  => 'c',
                        'traffic_source'      => 'google',
                        'nw'                  => 'g',
                        'ac'                  => 31723,
                        'aid'                 => 46055,
                        'kid'                 => 59942,
                        'clid'                => 'test',
                        'clid_source'         => 'gclid',
                        'sclid'               => 'generic-secondary-click-id',
                        'publisher'           => 'publisher',
                        'lpid'                => '214132-dfsgsfd-342',
                        'additional_channels' => ['kwc_123', 'kwc_128'],
                        'traffic_type'        => 'paid',
                        'contract_type'       => $this->stubs()
                            ->websiteSettings()
                            ->getGoogleAdSense()
                            ->getContractType()
                            ->getConversionLogValue(),
                        'ctid'                => 'custom_id',
                        'style_id'            => $adStyleId,
                        'client_id'           => $adClientId,
                        'domain'              => 'id.brand.com',
                        'locale'              => 'en_US',
                        'split_test_id'       => null,
                        'split_test_variant'  => null,
                        'template_variant'    => 'clt',
                        'accept_language'     => 'en-us,en;q=0.5',
                        'user_ip'             => '**************',
                        'country_code'        => 'NL',
                        'http_user_agent'     => [
                            'full'           => 'Symfony',
                            'os_family'      => 'Other',
                            'browser_family' => 'Other',
                            'device_family'  => 'Other',
                        ],
                        'http_host_client'    => 'https://id.brand.nl',
                        'tls_version'         => '1.2',
                        ...$extraLogData,
                    ],
                ],
            ],
        ];
    }
}
