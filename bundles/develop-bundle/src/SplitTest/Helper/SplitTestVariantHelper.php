<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\SplitTest\Helper;

use Symfony\Component\Yaml\Yaml;
use Visymo\DevelopBundle\SplitTest\FileSplitTest\FileSplitTestCollection;
use Visymo\DevelopBundle\SplitTest\FileSplitTest\FileSplitTestFactory;
use Visymo\Filesystem\File\FileInterface;
use Visymo\Filesystem\File\Iterator\FileIteratorFactory;

final class SplitTestVariantHelper
{
    /** @var string[] */
    private array $excludePaths = ['/var/cache', '/tests/', '/config/', '/public/'];

    /** @var string[] */
    private array $directories = ['bundles', 'resources', 'src'];

    /** @var array<string, string[]> */
    private array $mapping = [
        'php'       => [
            'containsVariant\(\'([^\']+)\'\)',
            'isVariantActive\(\'([^\']+)\'\)',
        ],
        'html.twig' => [
            'split_test_variant_active\(\'([^\']+)\'\)',
        ],
        'js'        => [
            'variant-([a-z0-9]+)',
        ],
        'scss'      => [
            'variant-([^,\s^:]+)',
        ],
        'json'      => [
            '"one_of_variants":\s*\[(.*?)\]',
        ],
    ];

    /** @var array<string, string> */
    private array $fileNameMapping = [
        'scss' => 'split-test-variant-([a-z0-9^,\s]+)',
    ];

    private int $maxDepth = 10;

    /** @var array<string, string> */
    private array $splitTestTicketMapping;

    /** @var array<string, FileSplitTestCollection> */
    private array $fileSplitTestCollections = [];

    public function __construct(
        private readonly string $projectDir,
        private readonly FileIteratorFactory $fileIteratorFactory,
        private readonly FileSplitTestFactory $fileSplitTestFactory,
        FileInterface $splitTestTicketMappingFile
    )
    {
        $splitTestTicketMappingContents = $splitTestTicketMappingFile->readContent();
        $this->splitTestTicketMapping = Yaml::parse($splitTestTicketMappingContents);
    }

    /** @return array<string, FileSplitTestCollection> */
    public function collectVariants(): array
    {
        foreach ($this->directories as $directory) {
            foreach ($this->mapping as $extension => $patterns) {
                $this->processDirectory($directory, $extension, $patterns);
            }
        }

        ksort($this->fileSplitTestCollections);

        return $this->fileSplitTestCollections;
    }

    /**
     * @param string[] $patterns
     */
    private function processDirectory(string $directory, string $extension, array $patterns): void
    {
        for ($currentDepth = 0; $currentDepth < $this->maxDepth; $currentDepth++) {
            $path = $this->createPath($directory, $extension, $currentDepth);
            $files = $this->fileIteratorFactory->create($path)->iterate();

            foreach ($files as $file) {
                if ($this->isExcluded($file)) {
                    continue;
                }

                if ($this->processFileName($extension, $file)) {
                    // If a split test was found in the file name, skip checking file contents as the file should
                    // only contain code relevant to the variant defined in the filename
                    continue;
                }

                foreach ($patterns as $pattern) {
                    $this->processFile($file, $pattern);
                }
            }
        }
    }

    private function createPath(string $directory, string $extension, int $depth): string
    {
        return sprintf('%s/%s%s/*.%s', $this->projectDir, $directory, str_repeat('/**', $depth), $extension);
    }

    private function isExcluded(FileInterface $file): bool
    {
        $filePath = $file->getFilePath();

        foreach ($this->excludePaths as $excludePath) {
            if (str_contains($filePath, $excludePath)) {
                return true;
            }
        }

        return false;
    }

    public function processFileName(string $extension, FileInterface $file): bool
    {
        $fileNamePattern = $this->fileNameMapping[$extension] ?? null;

        if ($fileNamePattern !== null) {
            preg_match_all(sprintf('/%s/s', $fileNamePattern), $file->getFileName(), $matches, PREG_OFFSET_CAPTURE);

            if (!isset($matches[1]) || $matches[1] === []) {
                return false;
            }

            $cleanedVariant = $matches[1][0][0];

            $this->fileSplitTestCollections[$cleanedVariant] ??= new FileSplitTestCollection(
                $this->splitTestTicketMapping[$cleanedVariant] ?? null,
            );
            $this->fileSplitTestCollections[$cleanedVariant]->add(
                $this->fileSplitTestFactory->create($file, 1),
            );

            return true;
        }

        return false;
    }

    private function processFile(FileInterface $file, string $pattern): void
    {
        $content = $file->readContent();
        preg_match_all(sprintf('/%s/s', $pattern), $content, $matches, PREG_OFFSET_CAPTURE);

        if (!isset($matches[1])) {
            return;
        }

        foreach ($matches[1] as $matchVariant) {
            $matchOffset = $matchVariant[1];
            $variants = array_map('trim', explode(',', trim($matchVariant[0])));

            foreach ($variants as $variant) {
                $cleanedVariant = trim($variant, '"');

                if ($cleanedVariant === '' || $cleanedVariant === 'visual_browser_test') {
                    continue;
                }

                [$before] = str_split($content, max(1, $matchOffset));
                $lineNumber = strlen($before) - strlen(str_replace("\n", '', $before)) + 1;

                $this->fileSplitTestCollections[$cleanedVariant] ??= new FileSplitTestCollection(
                    $this->splitTestTicketMapping[$cleanedVariant] ?? null,
                );
                $this->fileSplitTestCollections[$cleanedVariant]->add(
                    $this->fileSplitTestFactory->create($file, $lineNumber),
                );
            }
        }
    }
}
