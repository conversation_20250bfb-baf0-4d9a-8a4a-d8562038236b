<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Url;

use App\Debug\Request\DebugRequestInterface;
use App\Generic\Device\Device;
use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\Locale\Request\LocaleRequestInterface;
use App\Locale\Settings\LocaleSettings;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Request\SeaRequestInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use Symfony\Component\Routing\RouterInterface;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Account\LandingPageUrlAccountHelper;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRoute;

readonly class LandingPageUrlFactory
{
    public function __construct(
        private RouterInterface $router,
        private LandingPageUrlAccountHelper $landingPageUrlAccountHelper,
        private WebsiteConfigurationRepository $websiteConfigurationRepository
    )
    {
    }

    /**
     * @param mixed[] $brandConfig
     * @param mixed[] $domainConfig
     * @param mixed[] $accountIds
     */
    public function create(
        LandingPageRoute $landingPageRoute,
        string $host,
        LocaleSettings $localeSettings,
        array $brandConfig,
        array $domainConfig,
        array $accountIds,
        LandingPageUrlGeneratorOptions $options
    ): LandingPageUrl
    {
        $landingPageUrl = new LandingPageUrl($landingPageRoute->label);
        $accountSettings = $this->landingPageUrlAccountHelper->getAccountSettings(
            $options,
            $landingPageRoute,
            $landingPageUrl,
            $brandConfig,
            $domainConfig,
            $accountIds,
        );
        $websiteConfiguration = $this->websiteConfigurationRepository->getForBrand($options->brand);

        $accountCampaign = $this->landingPageUrlAccountHelper->selectRandomCampaignFromAccountSettings(
            $accountSettings,
            $accountSettings !== null ? $websiteConfiguration->getAccountConfig($accountSettings->id) : [],
        );

        $urlParameters = array_merge(
            [
                SeaRequestInterface::PARAMETER_ACCOUNT_ID    => $accountSettings?->id,
                SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => $accountCampaign,
            ],
            [
                SearchRequestInterface::PARAMETER_QUERY => $options->query,
            ],
            $landingPageRoute->urlParameters,
            [
                JsonTemplateRequestInterface::PARAMETER_TEMPLATE_VARIANT => $options->templateVariant,
            ],
            [
                DebugRequestInterface::PARAMETER_DEBUG_SPLIT_TEST_VARIANT => $options->splitTestVariant,
                DebugRequestInterface::PARAMETER_DEBUG_COUNTRY_CODE       => $localeSettings->locale->region->code,
                DebugRequestInterface::PARAMETER_DEBUG_INFO               => 1,
            ],
        );
        $route = $options->redirectUrl ? (string)$landingPageRoute->redirectRoute : $landingPageRoute->route;

        $landingPageUrl
            ->setAccountId($urlParameters[SeaRequestInterface::PARAMETER_ACCOUNT_ID] ?? null)
            ->setCampaign($urlParameters[SeaRequestInterface::PARAMETER_CAMPAIGN_NAME] ?? null);

        foreach (Device::cases() as $device) {
            if ($options->device !== null && $options->device !== $device) {
                continue;
            }

            $url = $this->getLandingPageUrlForDevice(
                urlParameters : $urlParameters,
                device        : $device,
                route         : $route,
                localeSettings: $localeSettings,
                host          : $host,
            );
            $landingPageUrl->addUrl($device->getLabel(), $url);
        }

        return $landingPageUrl;
    }

    /**
     * @param mixed[] $urlParameters
     */
    private function getLandingPageUrlForDevice(
        array $urlParameters,
        Device $device,
        string $route,
        LocaleSettings $localeSettings,
        string $host
    ): string
    {
        $urlParameters[SeaRequestInterface::PARAMETER_DEVICE] = $device->getShortValue();

        if (!$localeSettings->isDefault) {
            $urlParameters[LocaleRequestInterface::PARAMETER_LOCALE] = $localeSettings->locale->code;
        }

        $url = $this->router->generate($route, $urlParameters);

        return sprintf(
            'https://%s%s',
            $host,
            $url,
        );
    }
}
