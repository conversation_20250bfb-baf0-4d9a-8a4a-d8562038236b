<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl;

use App\Domain\Settings\DomainSettingsFactory;
use App\Http\Url\DevelopHostHelper;
use App\Locale\Settings\LocaleSettingsFactory;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRouteGenerator;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Url\LandingPageUrl;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Url\LandingPageUrlFactory;

readonly class LandingPageUrlGenerator
{
    public function __construct(
        private WebsiteConfigurationRepository $websiteConfigurationRepository,
        private DomainSettingsFactory $domainSettingsFactory,
        private LocaleSettingsHelperInterface $localeSettingsHelper,
        private LocaleSettingsFactory $localeSettingsFactory,
        private DevelopHostHelper $developHostHelper,
        private LandingPageRouteGenerator $landingPageRouteGenerator,
        private LandingPageUrlFactory $landingPageUrlFactory
    )
    {
    }

    /**
     * @return LandingPageUrl[]
     */
    public function generate(LandingPageUrlGeneratorOptions $options): array
    {
        $websiteConfiguration = $this->websiteConfigurationRepository->getForBrand($options->brand);

        if ($options->accountId !== null) {
            $accountConfig = $websiteConfiguration->getAccountConfig($options->accountId);

            // Account not found
            if ($accountConfig === null) {
                return [];
            }

            $accountIds = [$options->accountId];
        } else {
            $accountIds = $websiteConfiguration->getAccountIds();
        }

        $brandConfig = $websiteConfiguration->getBrandConfig();
        $domainConfig = $websiteConfiguration->getDomainConfig($options->domain);
        $localeConfig = $this->localeSettingsHelper->getLocaleConfig(
            $brandConfig,
            $domainConfig,
            $options->locale
        );

        $domainSettings = $this->domainSettingsFactory->create($domainConfig);
        $localeSettings = $this->localeSettingsFactory->create($localeConfig);
        $host = $options->devVmName !== null
            ? $this->developHostHelper->getHostWithDevVmName($domainSettings->host, $options->devVmName)
            : $domainSettings->host;

        $landingPageUrls = [];

        foreach ($this->landingPageRouteGenerator->generate($options) as $landingPageRoute) {
            $landingPageUrls[] = $this->landingPageUrlFactory->create(
                landingPageRoute: $landingPageRoute,
                host            : $host,
                localeSettings  : $localeSettings,
                brandConfig     : $brandConfig,
                domainConfig    : $domainConfig,
                accountIds      : $accountIds,
                options         : $options,
            );
        }

        return $landingPageUrls;
    }
}
