<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\LandingPage\Controller;

use App\Brand\Settings\BrandSettingsHelper;
use App\Http\Url\DevelopHostHelper;
use App\JavaScriptRelatedTerms\Settings\JavaScriptRelatedTermsSettings;
use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\Locale\Request\LocaleRequestInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Request\SeaRequestInterface;
use App\WebsiteSettings\Configuration\Exception\NoDomainFoundForLocaleException;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\DevelopBundle\BrandConfig\Locale\BrandConfigLocaleRepository;
use Visymo\DevelopBundle\Debug\LandingPageUrl\LandingPageUrlGenerator;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptionsFactory;
use Visymo\DevelopBundle\LandingPage\Request\LandingPageRequestInterface;

final class LandingPageController extends AbstractController
{
    public function __construct(
        private readonly JavaScriptRelatedTermsSettings $javaScriptRelatedTermsSettings,
        private readonly WebsiteConfigurationRepository $websiteConfigurationRepository,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly LandingPageUrlGenerator $landingPageUrlGenerator,
        private readonly LandingPageUrlGeneratorOptionsFactory $landingPageUrlGeneratorOptionsFactory,
        private readonly LandingPageRequestInterface $landingPageRequest,
        private readonly JsonTemplateRequestInterface $jsonTemplateRequest,
        private readonly LocaleRequestInterface $localeRequest,
        private readonly SeaRequestInterface $seaRequest,
        private readonly SearchRequestInterface $searchRequest,
        private readonly DevelopHostHelper $developHostHelper,
        private readonly LocaleSettingsHelperInterface $localeSettingsHelper,
        private readonly BrandConfigLocaleRepository $brandConfigLocaleRepository
    )
    {
    }

    #[Route(path: '/dev', name: 'route_develop_landing_page', methods: ['GET'])]
    public function index(): Response
    {
        $selectedBrand = $this->landingPageRequest->getBrand() ?? $this->brandSettingsHelper->getSettings()->getSlug();
        $locale = $this->localeRequest->getLocale() ?? $this->localeSettingsHelper->getSettings()->locale->code;
        $selectedLocale = $this->validateSelectedLocale(
            selectedBrand : $selectedBrand,
            selectedLocale: $locale,
        );
        $devVmName = $this->landingPageRequest->getProduction()
            ? null
            : $this->developHostHelper->getDevVmName();

        $landingPageUrlGeneratorOptions = $this->landingPageUrlGeneratorOptionsFactory->create(
            devVmName                : $devVmName,
            brand                    : $selectedBrand,
            domain                   : null,
            locale                   : $selectedLocale,
            accountId                : $this->seaRequest->getAccountId(),
            trafficSource            : null,
            device                   : null,
            query                    : $this->searchRequest->getQuery(),
            splitTestVariant         : $this->landingPageRequest->getSplitTestVariant(),
            templateVariant          : $this->jsonTemplateRequest->getTemplateVariant(),
            redirectUrl              : false,
            offlineConversionTracking: null,
        );

        $landingPages = $this->landingPageUrlGenerator->generate(
            $landingPageUrlGeneratorOptions,
        );

        return $this->render(
            '@develop/landing_page/landing_page.html.twig',
            [
                'landing_pages'                            => $landingPages,
                'brand_options'                            => $this->getBrandOptions($selectedBrand),
                'locale_options'                           => $this->getLocaleOptions($selectedBrand, $selectedLocale),
                'account_id'                               => $this->seaRequest->getAccountId(),
                'query'                                    => $this->searchRequest->getQuery(),
                'split_test_variant'                       => $this->landingPageRequest->getSplitTestVariant(),
                'template_variant'                         => $this->jsonTemplateRequest->getTemplateVariant(),
                'production'                               => $this->landingPageRequest->getProduction(),
                'javascript_related_terms_content_enabled' => $this->javaScriptRelatedTermsSettings->contentEnabled,
                'javascript_related_terms_search_enabled'  => $this->javaScriptRelatedTermsSettings->searchEnabled,
            ],
        );
    }

    /**
     * @return array<string, mixed>
     */
    private function getBrandOptions(string $selectedBrand): array
    {
        $brandOptions = [];

        foreach ($this->websiteConfigurationRepository->getAll() as $websiteConfiguration) {
            $brandSlug = $websiteConfiguration->getBrandSlug();
            $brandName = $websiteConfiguration->getBrandName();

            $brandOptions[$brandSlug] = [
                'label'    => $brandName,
                'selected' => $brandSlug === $selectedBrand,
            ];
        }

        ksort($brandOptions);

        return $brandOptions;
    }

    private function validateSelectedLocale(string $selectedBrand, string $selectedLocale): string
    {
        $websiteConfiguration = $this->websiteConfigurationRepository->getForBrand($selectedBrand);

        try {
            $websiteConfiguration->getDomainForLocale($selectedLocale);
        } catch (NoDomainFoundForLocaleException) {
            return array_rand($this->getLocaleOptions($selectedBrand));
        }

        return $selectedLocale;
    }

    /**
     * @return array<string, mixed>
     */
    private function getLocaleOptions(string $selectedBrand, ?string $selectedLocale = null): array
    {
        $localeOptions = [];

        foreach ($this->brandConfigLocaleRepository->getSupportedLocalesOfBrand($selectedBrand) as $locale) {
            $localeOptions[$locale->code] = [
                'label'    => $locale->code,
                'selected' => $locale->code === $selectedLocale,
            ];
        }

        return $localeOptions;
    }
}
