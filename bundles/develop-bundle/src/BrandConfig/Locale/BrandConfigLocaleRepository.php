<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\BrandConfig\Locale;

use App\Locale\Model\Language;
use App\Locale\Model\Locale;
use App\Locale\Model\LocaleFactory;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;

final class BrandConfigLocaleRepository
{
    /** @var Locale[] */
    private array $supportedLocales;

    /** @var Language[] */
    private array $supportedLanguages;

    public function __construct(
        private readonly WebsiteConfigurationFileRepository $websiteConfigurationFileRepository,
        private readonly LocaleFactory $localeFactory
    )
    {
    }

    /**
     * @return Language[]
     */
    public function getSupportedLanguages(): array
    {
        if (isset($this->supportedLanguages)) {
            return $this->supportedLanguages;
        }

        $supportedLanguages = [];

        foreach ($this->getSupportedLocales() as $locale) {
            $language = $locale->language;

            if (!isset($supportedLanguages[$language->code])) {
                $supportedLanguages[$language->code] = $language;
            }
        }

        $this->supportedLanguages = array_values($supportedLanguages);

        return $this->supportedLanguages;
    }

    /**
     * @return Locale[]
     */
    public function getSupportedLocalesOfBrand(string $slug): array
    {
        $brandData = $this->websiteConfigurationFileRepository->getForBrand($slug)->getContents();
        $locales = [];

        foreach ($brandData['domains'] ?? [] as $domainData) {
            foreach ($domainData['locales'] as $localeData) {
                $locales[$localeData['locale']] = true;
            }
        }

        return array_map(
            fn (string $code) => $this->localeFactory->create($code),
            array_keys($locales),
        );
    }

    /**
     * @return Locale[]
     */
    private function getSupportedLocales(): array
    {
        if (isset($this->supportedLocales)) {
            return $this->supportedLocales;
        }

        $supportedLocales = [];

        foreach ($this->websiteConfigurationFileRepository->getAvailableBrands() as $slug) {
            $brandData = $this->websiteConfigurationFileRepository->getForBrand($slug)->getContents();

            foreach ($brandData['domains'] ?? [] as $domainData) {
                foreach ($domainData['locales'] as $localeData) {
                    $supportedLocales[$localeData['locale']] = true;
                }
            }
        }

        $this->supportedLocales = array_map(
            fn (string $code) => $this->localeFactory->create($code),
            array_keys($supportedLocales),
        );

        return $this->supportedLocales;
    }
}
