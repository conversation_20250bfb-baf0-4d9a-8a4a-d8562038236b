<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateSitemap;

use App\Locale\Model\LocaleFactory;
use App\Search\Controller\SearchSeoController;
use App\Sitemap\Helper\SitemapHelper;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(
    name       : 'develop:generate-search-sitemap',
    description: '[PoC] Generate search sitemap for a certain brand'
)]
class GenerateSearchSitemapConsole extends Command
{
    private const string ARGUMENT_BRAND       = 'brand';
    private const string ARGUMENT_LOCALE      = 'locale';
    private const string ARGUMENT_SOURCE_FILE = 'source_file';

    public function __construct(
        private readonly DomainToBrandMapReader $domainToBrandMapReader,
        private readonly WebsiteConfigurationRepository $websiteConfigurationRepository,
        private readonly RouterInterface $router,
        private readonly SitemapHelper $sitemapHelper,
        private readonly LocaleFactory $localeFactory
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_BRAND,
                InputArgument::REQUIRED,
                'The brand which the sitemap is for',
            )
            ->addArgument(
                self::ARGUMENT_LOCALE,
                InputArgument::REQUIRED,
                'The locale the sitemap is generated for',
            )
            ->addArgument(
                self::ARGUMENT_SOURCE_FILE,
                InputArgument::REQUIRED,
                'The source file for the search pages',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $brandSlug = $input->getArgument(self::ARGUMENT_BRAND);
        $locale = $input->getArgument(self::ARGUMENT_LOCALE);
        $sourceFile = $input->getArgument(self::ARGUMENT_SOURCE_FILE);
        $brandSlugs = $this->domainToBrandMapReader->getBrands();

        if (!in_array($brandSlug, $brandSlugs, true)) {
            $output->writeln(sprintf('Brand %s not found in the list of brandSlugs', $brandSlug));

            return Command::FAILURE;
        }

        $websiteConfiguration = $this->websiteConfigurationRepository->getForBrand($brandSlug);
        $domain = $websiteConfiguration->getDomainForLocale($locale);
        $locale = $this->localeFactory->create($locale);
        $handle = fopen(sprintf('resource/%s', $sourceFile), 'rb', true);

        if ($handle === false) {
            $output->writeln(sprintf('Could not read source file: %s', $sourceFile));

            return Command::FAILURE;
        }

        $urls = [];

        while ($keyword = fgets($handle)) {
            $keyword = trim($keyword);
            $keyword = str_replace(' ', '+', $keyword);

            $url = $this->router->generate(
                'route_search_seo_mk',
                [
                    SearchSeoController::PARAMETER_QUERY => $keyword,
                ],
                UrlGeneratorInterface::ABSOLUTE_URL,
            );

            $urls[] = str_replace('http://localhost', sprintf('https://%s', $domain), $url);
        }

        $this->sitemapHelper->createXMLDocument(
            $urls,
            sprintf('search_terms_%s', strtolower($locale->region->code)),
        );

        return Command::SUCCESS;
    }
}
