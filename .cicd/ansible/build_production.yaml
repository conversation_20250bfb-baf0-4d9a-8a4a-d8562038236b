- name: Build production package
  gather_facts: false
  hosts: localhost

  vars_files:
    - vars.yaml

  tasks:
    - name: Remove all dotfiles except .cicd
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/dotfile
    - name: Install PHP dependencies using composer
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/composer
        tasks_from: install
    - name: Include Yarn task list
      ansible.builtin.include_tasks: yarn_tasks.yaml
    - name: Create release in Sentry
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/sentry
    - name: Create single package which contains all files and dependencies
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/tar
        tasks_from: create
