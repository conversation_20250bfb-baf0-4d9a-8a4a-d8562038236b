{# @var brand App\WebsiteSettings\Settings\Brand\BrandSettings #}
{# @var locale App\Locale\Model\Locale #}
{% block pre_render %}
    {# This block gives extending template the chance to set variables and call functions before rendering starts #}
{% endblock %}
<!doctype html>
<html lang="{{ locale.isoCode }}"{% if locale.language.isRightToLeft %} dir="rtl"{% endif %} class="{{ render_template_html_class() }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, minimal-ui">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge">
        <meta name="google" content="notranslate">
        <meta name="google" content="nopagereadaloud">
        <script>
            {% apply js_try_catch %}
            var cookieDomain = {{ app.request.host|json_encode|raw }};
            var pvid = {{ statistics_pageview_id()|json_encode|raw }};
            var vid = {{ statistics_visit_id()|json_encode|raw }};
            {% endapply %}
        </script>
        {{ brand_asset_style() }}
        {{ split_test_style() }}
        {{ render_template_headers() }}
        {% block header_scripts %}{% endblock %}
        <link rel="icon" href="{{ path('route_assets_favicon_ico') }}" sizes="32x32">
        <link rel="icon" href="{{ brand_image_base64('favicon.svg') }}" type="image/svg+xml">
        <link rel="apple-touch-icon" href="{{ path('route_assets_apple_touch_icon_png') }}">
        <meta name="msapplication-TileColor" content="{{ brand_color() }}">
        {% if is_contract_type_online() %}
            <meta name="google-adsense-account" content="ca-pub-****************">
        {% else %}
            <meta name="google-adsense-account" content="sites-****************">
        {% endif %}
    </head>
    <body class="body">
        {% block body %}{% endblock %}
        {{ render_template_footers() }}
        {% block footer_scripts %}{% endblock %}
        {{ split_test_script() }}
    </body>
</html>
